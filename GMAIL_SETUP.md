# Gmail Integration Setup Guide

## Current Issue
The Gmail integration is using OAuth credentials from an old Google Cloud project (************) instead of the new project (partners-in-biz-85059). This is causing authentication issues.

## Quick Fix Steps

### 1. Update OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Select project: **partners-in-biz-85059**
3. Navigate to **APIs & Services > Credentials**
4. Create or find your OAuth 2.0 Client ID
5. Update the following in `/apps/pib/.env`:

```env
GOOGLE_CLIENT_ID=your-new-client-id
GOOGLE_CLIENT_SECRET=your-new-client-secret
```

### 2. Restart Development Server

After updating the .env file:

```bash
# Stop the server (Ctrl+C)
# Then restart:
pnpm dev
```

### 3. Clear Browser Data

1. Clear browser cache and cookies for localhost:3006
2. Or use incognito/private browsing mode

### 4. Re-authenticate Gmail

1. Go to http://localhost:3006/user/integrations
2. Remove the existing Gmail integration (if any)
3. Add Gmail integration again
4. Complete the OAuth flow with your Gmail account

## Debugging Tools

### Check Current Status
- The inbox page now shows debug information at the top
- Look for the yellow debug box showing OAuth configuration status

### Run Debug Script
```bash
node scripts/debug-gmail.js
```

### Check API Debug Endpoint
Visit: http://localhost:3006/api/integrations/gmail/debug

## Expected OAuth Configuration

Your OAuth 2.0 Client should have these settings:

**Authorized JavaScript origins:**
- http://localhost:3006

**Authorized redirect URIs:**
- http://localhost:3006/api/integrations/gmail/oauth/callback

**Scopes required:**
- https://www.googleapis.com/auth/gmail.readonly
- https://www.googleapis.com/auth/gmail.send
- https://www.googleapis.com/auth/gmail.modify
- https://www.googleapis.com/auth/gmail.labels
- https://www.googleapis.com/auth/gmail.compose
- https://www.googleapis.com/auth/userinfo.email
- https://www.googleapis.com/auth/userinfo.profile

## Verification

After completing the setup:

1. The debug info should show:
   - ✅ Correct project ID (not ************)
   - ✅ Valid OAuth credentials present
   - ✅ Valid access token
   - ✅ Gmail authenticated

2. Gmail messages should display in the inbox
3. You should be able to send emails

## Troubleshooting

If issues persist:

1. Check browser console for errors
2. Verify Firebase messaging sender ID matches (line 7 in .env)
3. Ensure all Google APIs are enabled in the Cloud Console:
   - Gmail API
   - Google+ API (for user info)
4. Check Firestore for the integration record
5. Look at server logs for detailed error messages

## Important Note

The OAuth credentials are loaded when the server starts. Any changes to the .env file require a server restart to take effect.
