node_modules
*.log
*.zip
.nuxt
nuxt.d.ts
.output
dist
.env
.env.local
.env.test.local
.data
*Zone.Identifier
.eslintcache

# Test results and artifacts
test-results/
coverage/
playwright-report/
test-screenshots/
test-videos/

# Browser cache for testing
browser-cache/

# Firebase emulator data
firebase-debug.log
firestore-debug.log
ui-debug.log

# Test credentials and configuration
.claude/credentials.json
.claude/.credentials.enc
.claude/test-config.json.backup
.auth/
