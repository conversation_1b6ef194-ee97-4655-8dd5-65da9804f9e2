# PIB Calendar Test Environment Variables
# Copy this file to .env.test.local and fill in your actual values
# This file contains sensitive information and should not be committed to git

# Application Settings
NODE_ENV=test
NUXT_PUBLIC_APP_URL=http://localhost:3006

# Firebase Testing Configuration
NUXT_PUBLIC_FIREBASE_PROJECT_ID=your-test-project-id
NUXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NUXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-test-project.firebaseapp.com
NUXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-test-project.appspot.com
NUXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NUXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef
NUXT_PUBLIC_USE_FIREBASE_EMULATOR=true

# Google Calendar Integration (for testing)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALENDAR_API_KEY=your-google-calendar-api-key

# Test User Credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=MPStander@3

# Gmail Integration Testing
GMAIL_CLIENT_ID=your-gmail-client-id.apps.googleusercontent.com
GMAIL_CLIENT_SECRET=your-gmail-client-secret

# Database Testing
# These should point to test/emulator instances, never production
FIREBASE_ADMIN_PRIVATE_KEY_ID=your-admin-key-id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-admin-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_CLIENT_ID=123456789

# AI Module Testing
ANTHROPIC_API_KEY=your-test-anthropic-api-key
OPENAI_API_KEY=your-test-openai-api-key

# Testing Framework Settings
PLAYWRIGHT_BROWSERS_PATH=./browser-cache
PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false

# Performance Testing
LIGHTHOUSE_CI_SERVER_BASE_URL=http://localhost:3006

# Security Testing
SECURITY_SCAN_ENABLED=true
XSS_PROTECTION_ENABLED=true