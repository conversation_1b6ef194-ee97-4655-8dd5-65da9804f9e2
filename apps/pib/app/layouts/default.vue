<script setup lang="ts">
const menu = [
  {
    label: 'Dashboards',
    icon: 'solar:sidebar-minimalistic-linear',
    links: [
      {
        label: 'Personal',
        icon: 'solar:box-minimalistic-linear',
        children: [
          {
            label: 'Personal v1',
            to: '/dashboards',
          },
          {
            label: 'Personal v2',
            to: '/dashboards/personal-2',
          },
          {
            label: 'Personal v3',
            to: '/dashboards/personal-3',
          },
        ],
      },
      {
        label: 'Banking',
        icon: 'solar:buildings-linear',
        children: [
          {
            label: 'Account balance',
            to: '/dashboards/balance',
          },
          {
            label: 'Account overview',
            to: '/dashboards/banking-1',
          },
          {
            label: 'Account expenses',
            to: '/dashboards/banking-2',
          },
          {
            label: 'Account aggregator',
            to: '/dashboards/banking-5',
          },
        ],
      },
      {
        label: 'Transactions',
        icon: 'solar:card-linear',
        children: [
          {
            label: 'Overview',
            to: '/dashboards/overview',
          },
          {
            label: 'Quickview',
            to: '/dashboards/quickview',
          },
          {
            label: 'Account tracking',
            to: '/dashboards/banking-4',
          },
        ],
      },
      {
        label: 'Finance',
        icon: 'solar:chart-linear',
        children: [
          {
            label: 'Analytics',
            to: '/dashboards/analytics',
          },
          {
            label: 'Stock market',
            to: '/dashboards/stocks',
          },
          {
            label: 'Stock tracking',
            to: '/dashboards/trading',
          },
        ],
      },
      {
        label: 'Business',
        icon: 'solar:case-linear',
        children: [
          {
            label: 'Flight booking',
            to: '/dashboards/flights',
          },
          {
            label: 'Company overview',
            to: '/dashboards/company',
          },
          {
            label: 'Human Resources',
            to: '/dashboards/human-resources',
          },
          {
            label: 'Course overview',
            to: '/dashboards/course',
          },
          {
            label: 'Job search',
            to: '/dashboards/jobs',
          },
        ],
      },
      {
        label: 'Commerce',
        icon: 'solar:cart-3-linear',
        children: [
          {
            label: 'Sales overview',
            to: '/dashboards/sales',
          },
          {
            label: 'Store overview',
            to: '/dashboards/ecommerce',
          },
        ],
      },
      {
        label: 'Lifestyle',
        icon: 'solar:confetti-minimalistic-linear',
        children: [
          {
            label: 'Influencer',
            to: '/dashboards/influencer',
          },
          {
            label: 'Hobbies',
            to: '/dashboards/hobbies',
          },
          {
            label: 'Health',
            to: '/dashboards/health',
          },
          {
            label: 'Writer',
            to: '/dashboards/writer',
          },
          {
            label: 'Video log',
            to: '/dashboards/video',
          },
          {
            label: 'Soccer',
            to: '/dashboards/soccer',
          },
        ],
      },
    ],
  },
  {
    label: 'Applications',
    icon: 'solar:widget-2-linear',
    links: [
      {
        label: 'AI assistant',
        icon: 'solar:sticker-smile-square-linear',
        children: [
          {
            label: 'AI chat v1',
            to: '/ai',
          },
          {
            label: 'AI chat v2',
            to: '/layouts/ai/ui',
          },
        ],
      },
      {
        label: 'Calendar',
        icon: 'solar:calendar-linear',
        to: '/calendar',
      },
      {
        label: 'Food delivery',
        icon: 'solar:map-point-wave-linear',
        to: '/dashboards/delivery',
      },
      {
        label: 'Direct messaging',
        icon: 'solar:chat-square-linear',
        children: [
          {
            label: 'Sidebar chat',
            to: '/ai',
          },
          {
            label: 'Standalone chat',
            to: '/dashboards/messaging-2',
          },
        ],
      },
      {
        label: 'Form layouts',
        icon: 'solar:notes-linear',
        children: [
          {
            label: 'Company info',
            to: '/layouts/form-1',
          },
          {
            label: 'Create doctor',
            to: '/layouts/form-2',
          },
          {
            label: 'Checkout order',
            to: '/layouts/form-3',
          },
          {
            label: 'Create event',
            to: '/layouts/form-4',
          },
          {
            label: 'Password gen',
            to: '/layouts/form-5',
          },
          {
            label: 'Create meeting',
            to: '/layouts/form-6',
          },
          {
            label: 'Create contact',
            to: '/layouts/contacts/create',
          },
          {
            label: 'Edit user',
            to: '/layouts/user-edit',
          },
          {
            label: 'Edit company',
            to: '/layouts/company-edit',
          },
        ],
      },
      {
        label: 'Multistep wizard',
        icon: 'solar:bolt-linear',
        to: '/wizard',
      },
      {
        label: 'Widgets',
        icon: 'solar:widget-add-linear',
        children: [
          {
            label: 'UI widgets',
            to: '/dashboards/widgets',
          },
          {
            label: 'Creative widgets',
            to: '/dashboards/widgets/creative',
          },
          {
            label: 'List widgets',
            to: '/dashboards/widgets/list',
          },
        ],
      },
      {
        label: 'Apex charts',
        icon: 'solar:pie-chart-2-linear',
        to: '/dashboards/charts',
      },
      {
        label: 'Starters',
        icon: 'solar:widget-linear',
        children: [
          {
            label: 'Sidebar layout',
            to: '/starters/sidebar',
          },
          {
            label: 'Collapse layout',
            to: '/starters/collapse',
          },
          {
            label: 'Sidenav layout',
            to: '/starters/sidenav',
          },
          {
            label: 'Topnav layout',
            to: '/starters/topnav',
          },
          {
            label: 'Toptabs layout',
            to: '/starters/topnav-slim',
          },
        ],
      },
    ],
  },
  {
    label: 'Lists & grids',
    icon: 'solar:align-vertical-spacing-linear',
    links: [
      {
        label: 'List view',
        icon: 'solar:slider-horizontal-linear',
        children: [
          {
            label: 'List view v1',
            to: '/layouts',
          },
          {
            label: 'List view v2',
            to: '/layouts/list-view-2',
          },
          {
            label: 'List view v3',
            to: '/layouts/list-view-3',
          },
          {
            label: 'List view v4',
            to: '/layouts/list-view-4',
          },
        ],
      },
      {
        label: 'Flex list',
        icon: 'solar:mirror-left-linear',
        children: [
          {
            label: 'Flex list v1',
            to: '/layouts/flex-list-1',
          },
          {
            label: 'Flex list v2',
            to: '/layouts/flex-list-2',
          },
          {
            label: 'Flex list v3',
            to: '/layouts/flex-list-3',
          },
        ],
      },
      {
        label: 'Table list',
        icon: 'solar:slider-minimalistic-horizontal-linear',
        children: [
          {
            label: 'Table list v1',
            to: '/layouts/table-list-1',
          },
          {
            label: 'Table list v2',
            to: '/layouts/table-list-2',
          },
          {
            label: 'Table list v3',
            to: '/layouts/table-list-3',
          },
        ],
      },
      {
        label: 'Card grid',
        icon: 'solar:posts-carousel-horizontal-linear',
        children: [
          {
            label: 'Card grid v1',
            to: '/layouts/card-grid-1',
          },
          {
            label: 'Card grid v2',
            to: '/layouts/card-grid-2',
          },
          {
            label: 'Card grid v3',
            to: '/layouts/card-grid-3',
          },
          {
            label: 'Card grid v4',
            to: '/layouts/card-grid-4',
          },
        ],
      },
      {
        label: 'Tile grid',
        icon: 'solar:posts-carousel-vertical-linear',
        children: [
          {
            label: 'Tile grid v1',
            to: '/layouts/tile-grid-1',
          },
          {
            label: 'Tile grid v2',
            to: '/layouts/tile-grid-2',
          },
          {
            label: 'Tile grid v3',
            to: '/layouts/tile-grid-3',
          },
        ],
      },
      {
        label: 'User grid',
        icon: 'solar:users-group-rounded-linear',
        children: [
          {
            label: 'User grid v1',
            to: '/layouts/user-grid-1',
          },
          {
            label: 'User grid v2',
            to: '/layouts/user-grid-2',
          },
          {
            label: 'User grid v3',
            to: '/layouts/user-grid-3',
          },
          {
            label: 'User grid v4',
            to: '/layouts/user-grid-4',
          },
        ],
      },
      {
        label: 'Placeloads',
        icon: 'solar:refresh-circle-linear',
        children: [
          {
            label: 'Placeloads v1',
            to: '/layouts/placeload-1',
          },
          {
            label: 'Placeloads v2',
            to: '/layouts/placeload-2',
          },
          {
            label: 'Placeloads v3',
            to: '/layouts/placeload-3',
          },
          {
            label: 'Placeloads v4',
            to: '/layouts/placeload-4',
          },
        ],
      },
    ],
  },
  {
    label: 'Business',
    icon: 'solar:suitcase-lines-linear',
    links: [
      {
        label: 'Projects overview',
        icon: 'solar:suitcase-linear',
        children: [
          {
            label: 'Projects v1',
            to: '/layouts/projects',
          },
          {
            label: 'Projects v2',
            to: '/layouts/projects/project-list-2',
          },
          {
            label: 'Projects v3',
            to: '/layouts/projects/project-list-3',
          },
        ],
      },
      {
        label: 'Project details',
        icon: 'solar:plate-linear',
        to: '/layouts/projects/details',
      },
      {
        label: 'Kanban board',
        icon: 'solar:widget-4-linear',
        to: '/layouts/projects/board',
      },
      {
        label: 'Accounts',
        icon: 'solar:key-minimalistic-square-2-linear',
        children: [
          {
            label: 'Account list',
            to: '/layouts/accounts',
          },
          {
            label: 'Linked accounts',
            to: '/layouts/accounts/linked',
          },
          {
            label: 'Account rules',
            to: '/layouts/accounts/rules',
          },
        ],
      },
      {
        label: 'Credit cards',
        icon: 'solar:card-linear',
        children: [
          {
            label: 'Card list',
            to: '/layouts/cards',
          },
          {
            label: 'New card',
            to: '/layouts/card/new',
          },
        ],
      },
      {
        label: 'Transactions',
        icon: 'solar:transfer-horizontal-linear',
        children: [
          {
            label: 'Transaction list',
            to: '/layouts/transactions',
          },
          {
            label: 'Outgoing payments',
            to: '/layouts/payments',
          },
          {
            label: 'Incoming payments',
            to: '/layouts/payments/incoming',
          },
          {
            label: 'Recipients',
            to: '/layouts/payments/recipients',
          },
        ],
      },
      {
        label: 'Wizards',
        icon: 'solar:bolt-linear',
        children: [
          {
            label: 'Send money',
            to: '/layouts/send',
          },
          {
            label: 'Receive money',
            to: '/layouts/receive',
          },
          {
            label: 'Invite people',
            to: '/layouts/invite',
          },
        ],
      },
      {
        label: 'Miscellaneous',
        icon: 'solar:shield-check-linear',
        children: [
          {
            label: 'Members',
            to: '/layouts/members',
          },
          {
            label: 'Investments',
            to: '/layouts/invest',
          },
          {
            label: 'Credit request',
            to: '/layouts/credit',
          },
          {
            label: 'Personal vault',
            to: '/layouts/vault',
          },
        ],
      },
    ],
  },
  {
    label: 'Utility',
    icon: 'solar:graph-new-linear',
    links: [
      {
        label: 'Login',
        icon: 'solar:lock-linear',
        children: [
          {
            label: 'Login v1',
            to: '/auth',
          },
          {
            label: 'Login v2',
            to: '/auth/login-1',
          },
          {
            label: 'Login v3',
            to: '/auth/login-2',
          },
          {
            label: 'Login v4',
            to: '/auth/login-3',
          },
        ],
      },
      {
        label: 'Signup',
        icon: 'solar:key-minimalistic-linear',
        children: [
          {
            label: 'Signup v1',
            to: '/auth/signup-1',
          },
          {
            label: 'Signup v2',
            to: '/auth/signup-2',
          },
          {
            label: 'Signup v3',
            to: '/auth/signup-3',
          },
        ],
      },
      {
        label: 'Forgot password',
        icon: 'solar:refresh-square-linear',
        to: '/auth/recover',
      },
      {
        label: 'Account',
        icon: 'solar:user-linear',
        children: [
          {
            label: 'Profile',
            to: '/layouts/profile',
          },
          {
            label: 'Profile notifications',
            to: '/layouts/profile-notifications',
          },
          {
            label: 'Profile settings',
            to: '/layouts/profile-settings',
          },
          {
            label: 'Profile edit',
            to: '/layouts/profile-edit',
          },
          {
            label: 'User info',
            to: '/layouts/user-details',
          },
          {
            label: 'Company info',
            to: '/layouts/company',
          },
        ],
      },
      {
        label: 'Subpages',
        icon: 'solar:document-linear',
        children: [
          {
            label: 'Documents',
            to: '/layouts/documents',
          },
          {
            label: 'Downloads',
            to: '/layouts/downloads',
          },
          {
            label: 'Integrations',
            to: '/layouts/integrations',
          },
          {
            label: 'Offers',
            to: '/layouts/offers',
          },
          {
            label: 'SaaS billing',
            to: '/layouts/utility-billing',
          },
        ],
      },
      {
        label: 'Utility',
        icon: 'solar:home-smile-linear',
        children: [
          {
            label: 'Action v1',
            to: '/layouts/utility-action-1',
          },
          {
            label: 'Action v2',
            to: '/layouts/utility-action-2',
          },
          {
            label: 'Promotion',
            to: '/layouts/utility-promotion',
          },
          {
            label: 'Confirm action',
            to: '/layouts/utility-confirm',
          },
          {
            label: 'Invoice v1',
            to: '/layouts/utility-invoice',
          },
          {
            label: 'Invoice v2',
            to: '/layouts/utility-invoice-2',
          },
          {
            label: 'System status',
            to: '/layouts/utility-status',
          },
          {
            label: 'System error',
            to: '/layouts/utility-error',
          },
          {
            label: 'Search results',
            to: '/layouts/search-results',
          },
          {
            label: 'Search empty',
            to: '/layouts/search-empty',
          },
        ],
      },
      {
        label: 'Settings',
        icon: 'solar:settings-linear',
        to: '/layouts/settings',
      },
      {
        label: 'Preferences',
        icon: 'solar:tuning-2-linear',
        to: '/layouts/preferences',
      },
      {
        label: 'Onboarding',
        icon: 'solar:plain-3-linear',
        children: [
          {
            label: '2 factor auth',
            to: '/layouts/onboarding-1',
          },
          {
            label: 'Plan selection',
            to: '/layouts/onboarding-2',
          },
          {
            label: 'Role selection',
            to: '/layouts/onboarding-3',
          },
        ],
      },
    ],
  },
]

const isSwitcherOpen = useColorSwitcherOpen()

const route = useRoute()
const router = useRouter()
const sidebarId = ref(getRouteSidebarId())

// Calendar management state
const {
  calendars,
  selectedCalendarId,
  visibleCalendars,
  isLoading: calendarsLoading,
  createCalendar,
  updateCalendar,
  deleteCalendar,
  toggleCalendarVisibility,
  setDefaultCalendar,
} = useCalendarManagement()

const {
  isConnecting: isGoogleCalendarConnecting,
  isConnected: isGoogleCalendarConnected,
  connectGoogleCalendar,
  disconnectGoogleCalendar,
  syncFromGoogle,
  syncToGoogle,
  currentIntegration: googleCalendarIntegration,
} = useGoogleCalendar()

const {
  integrations: calendarIntegrations,
  loading: integrationsLoading,
  isProviderConnected,
  getDefaultForProvider,
  activeIntegrations,
} = useCalendarIntegrations()

// Calendar UI state
const showCalendarCreationModal = ref(false)
const showIntegrationModal = ref(false)
const calendarToEdit = ref(null)
const showCalendarContextMenu = ref(false)
const contextMenuCalendar = ref(null)

// Calendar colors
const calendarColors = [
  { name: 'Blue', value: '#3B82F6' },
  { name: 'Green', value: '#10B981' },
  { name: 'Purple', value: '#8B5CF6' },
  { name: 'Pink', value: '#EC4899' },
  { name: 'Yellow', value: '#F59E0B' },
  { name: 'Red', value: '#EF4444' },
  { name: 'Indigo', value: '#6366F1' },
  { name: 'Gray', value: '#6B7280' },
]

// Calendar creation form
const newCalendarForm = ref({
  name: '',
  description: '',
  color: '#3B82F6',
  isDefault: false,
  linkToGoogle: false,
})

const toaster = useToast()

watch(() => route.path, () => {
  sidebarId.value = getRouteSidebarId()
})

// Watch for calendar selection changes
watch(selectedCalendarId, () => {
  handleCalendarNavigation()
})

function getRouteSidebarId() {
  if (route.path.startsWith('/ai')) {
    return 'Messaging'
  }
  if (route.path.startsWith('/inbox')) {
    return 'Inbox'
  }
  if (route.path.startsWith('/dashboards/inbox')) {
    return 'Inbox'
  }
  if (route.path.startsWith('/calendar')) {
    return 'Calendar'
  }
  if (route.path.startsWith('/dashboards/map')) {
    return 'Map'
  }

  // search for the active menu item
  for (const item of menu) {
    if (item.links.some(link => link.to === route.path || (link.children && link.children.some(child => child.to === route.path)))) {
      return item.label
    }
  }

  return 'Dashboards'
}

// Gmail sidebar handlers
function handleGmailFilter(filter: string) {
  router.push({ path: '/inbox', query: { filter } })
}

function handleCalendarNavigation() {
  router.push('/calendar')
}

// Calendar handlers
async function handleCreateCalendar() {
  try {
    // Create the calendar first
    const calendarData = {
      name: newCalendarForm.value.name,
      description: newCalendarForm.value.description,
      color: newCalendarForm.value.color,
      isDefault: newCalendarForm.value.isDefault,
    }

    // If linking to Google Calendar is requested
    if (newCalendarForm.value.linkToGoogle && isGoogleCalendarConnected.value && googleCalendarIntegration.value) {
      calendarData.integrationId = googleCalendarIntegration.value.id
      calendarData.provider = 'google'

      // Create calendar in Google Calendar first
      try {
        const googleCalendarEvents = useGoogleCalendarEvents()
        const googleCalendarResponse = await googleCalendarEvents.createCalendar({
          summary: calendarData.name,
          description: calendarData.description,
        })

        if (googleCalendarResponse?.id) {
          calendarData.externalCalendarId = googleCalendarResponse.id
        }
      }
      catch (googleError) {
        console.warn('Failed to create Google Calendar, creating local calendar only:', googleError)
      }
    }

    await createCalendar(calendarData)
    showCalendarCreationModal.value = false

    // Reset form
    newCalendarForm.value = {
      name: '',
      description: '',
      color: '#3B82F6',
      isDefault: false,
      linkToGoogle: false,
    }

    toaster.add({
      title: 'Calendar Created',
      description: newCalendarForm.value.linkToGoogle && calendarData.externalCalendarId
        ? 'Your calendar has been created and linked to Google Calendar.'
        : 'Your calendar has been created successfully.',
      color: 'success',
      icon: 'lucide:check-circle',
    })
  }
  catch (error) {
    toaster.add({
      title: 'Error',
      description: 'Failed to create calendar.',
      color: 'danger',
      icon: 'lucide:x-circle',
    })
  }
}

async function handleUpdateCalendar() {
  if (!calendarToEdit.value)
    return

  try {
    await updateCalendar(calendarToEdit.value.id, calendarToEdit.value)
    showCalendarCreationModal.value = false
    calendarToEdit.value = null
    toaster.add({
      title: 'Calendar Updated',
      description: 'Your calendar has been updated successfully.',
      color: 'success',
      icon: 'lucide:check-circle',
    })
  }
  catch (error) {
    toaster.add({
      title: 'Error',
      description: 'Failed to update calendar.',
      color: 'danger',
      icon: 'lucide:x-circle',
    })
  }
}

async function handleDeleteCalendar(calendarId: string) {
  try {
    await deleteCalendar(calendarId)
    toaster.add({
      title: 'Calendar Deleted',
      description: 'Your calendar has been deleted successfully.',
      color: 'success',
      icon: 'lucide:check-circle',
    })
  }
  catch (error) {
    toaster.add({
      title: 'Error',
      description: 'Failed to delete calendar.',
      color: 'danger',
      icon: 'lucide:x-circle',
    })
  }
}

function openCalendarCreationModal() {
  calendarToEdit.value = null
  newCalendarForm.value = {
    name: '',
    description: '',
    color: '#3B82F6',
    isDefault: false,
    linkToGoogle: false,
  }
  showCalendarCreationModal.value = true
}

function openCalendarEditModal(calendar: any) {
  calendarToEdit.value = { ...calendar }
  newCalendarForm.value = { ...calendar }
  showCalendarCreationModal.value = true
}

function selectCalendar(calendarId: string) {
  selectedCalendarId.value = calendarId
  handleCalendarNavigation()
}

// Google Calendar integration
async function handleConnectGoogleCalendar() {
  try {
    await connectGoogleCalendar()
  }
  catch (error) {
    console.error('Failed to connect Google Calendar:', error)
  }
}

async function handleSyncGoogleCalendar() {
  try {
    await syncFromGoogle()
    toaster.add({
      title: 'Calendar Synced',
      description: 'Your Google Calendar has been synced successfully.',
      color: 'success',
      icon: 'lucide:sync',
    })
  }
  catch (error) {
    toaster.add({
      title: 'Sync Failed',
      description: 'Failed to sync with Google Calendar.',
      color: 'danger',
      icon: 'lucide:x-circle',
    })
  }
}
</script>

<template>
  <TairoSidebarLayout
    v-slot="{ toggleMobileNav }"
    v-model="sidebarId"
    :class="[
      sidebarId === 'Messaging' ? '[--tairo-sidebar-subsidebar-width:4.5rem]' : '',
      sidebarId === 'Inbox' ? '[--tairo-sidebar-subsidebar-width:3.5rem]' : '',
      sidebarId === 'Calendar' ? '[--tairo-sidebar-subsidebar-width:3.5rem]' : '',
      sidebarId === 'Map' ? '[--tairo-sidebar-subsidebar-width:0rem]' : '',
      sidebarId === 'Dashboards' ? '[--tairo-sidebar-subsidebar-width:0rem]' : '',
    ]"
  >
    <TairoSidebarNav>
      <TairoSidebar>
        <NuxtLink to="/" class="flex items-center justify-center size-14 shrink-0">
          <TairoLogo class="size-8 text-primary-heavy dark:text-primary-light" />
        </NuxtLink>

        <TairoSidebarLinks class="overflow-y-auto nui-slimscroll">
          <BaseTooltip
            v-for="item in menu"
            :key="item.label"
            :content="item.label"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <TairoSidebarTrigger :value="item.label">
              <Icon :name="item.icon" class="size-5" />
            </TairoSidebarTrigger>
          </BaseTooltip>

          <BaseTooltip
            content="Messaging"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <TairoSidebarTrigger value="Messaging" to="/ai">
              <Icon name="solar:chat-round-unread-linear" class="size-5" />
            </TairoSidebarTrigger>
          </BaseTooltip>

          <BaseTooltip
            content="Inbox"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <TairoSidebarTrigger value="Inbox" to="/inbox">
              <Icon name="solar:letter-unread-linear" class="size-5" />
            </TairoSidebarTrigger>
          </BaseTooltip>

          <BaseTooltip
            content="Calendar"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <TairoSidebarTrigger value="Calendar" to="/calendar">
              <Icon name="solar:calendar-linear" class="size-5" />
            </TairoSidebarTrigger>
          </BaseTooltip>
          <BaseTooltip
            content="Map"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <TairoSidebarTrigger value="Map" to="/dashboards/map">
              <Icon name="solar:map-point-wave-linear" class="size-5" />
            </TairoSidebarTrigger>
          </BaseTooltip>
        </TairoSidebarLinks>

        <TairoSidebarLinks class="shrink-0 mt-auto">
          <BaseTooltip
            content="Customize"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <TairoSidebarLink tabindex="0" @click="isSwitcherOpen = true">
              <Icon name="solar:palette-round-linear" class="size-5" />
            </TairoSidebarLink>
          </BaseTooltip>
          <BaseTooltip
            content="Settings"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <TairoSidebarLink to="/user">
              <Icon name="solar:settings-linear" class="size-5" />
            </TairoSidebarLink>
          </BaseTooltip>
          <TairoSidebarLink>
            <BaseThemeToggle class="scale-90" />
          </TairoSidebarLink>
          <TairoSidebarLink to="/profile">
            <BaseChip size="sm" pulse color="custom" :offset="3" class="text-green-600 flex items-center justify-center">
              <BaseAvatar
                size="xs"
                src="/img/avatars/10.svg"
              />
            </BaseChip>
          </TairoSidebarLink>
        </TairoSidebarLinks>
      </TairoSidebar>

      <TairoSidebarSubsidebar v-for="item in menu" :key="item.label" :value="item.label">
        <TairoSidebarSubsidebarHeader>
          {{ item.label }}
        </TairoSidebarSubsidebarHeader>
        <TairoSidebarSubsidebarContent>
          <template v-for="link in item.links" :key="link.label">
            <TairoSidebarSubsidebarLink v-if="!link.children" :to="link.to">
              <Icon :name="link.icon" class="size-4 text-muted-500 dark:text-muted-400" />
              <span>{{ link.label }}</span>
            </TairoSidebarSubsidebarLink>
            <TairoSidebarSubsidebarCollapsible
              v-else
              :children="link.children"
              :default-open="link.children.some((child) => child.to === $route.path) || undefined"
            >
              <template #trigger>
                <TairoSidebarSubsidebarCollapsibleTrigger>
                  <Icon :name="link.icon" class="size-4 text-muted-500 dark:text-muted-400" />
                  <span>{{ link.label }}</span>
                </TairoSidebarSubsidebarCollapsibleTrigger>
              </template>
            </TairoSidebarSubsidebarCollapsible>
          </template>
        </TairoSidebarSubsidebarContent>
      </TairoSidebarSubsidebar>

      <TairoSidebarSubsidebar value="Messaging">
        <SubsidebarMessaging />
      </TairoSidebarSubsidebar>

      <TairoSidebarSubsidebar value="Inbox" class="flex flex-col items-center">
        <TairoSidebarSubsidebarContent>
          <div class="flex h-12 w-full items-center justify-center shrink-0">
            <BaseButton size="icon-sm" rounded="full" variant="primary">
              <Icon name="lucide:plus" class="size-4" />
            </BaseButton>
          </div>
          <div class="flex h-12 w-full items-center justify-center shrink-0">
            <BaseTooltip
              content="Received"
              variant="dark"
              :bindings="{
                content: { side: 'left' },
                portal: { disabled: true },
              }"
            >
              <BaseButton
                size="icon-sm"
                rounded="md"
                variant="ghost"
                :class="{ 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400': route.query.filter === 'inbox' || (!route.query.filter && route.path === '/inbox') }"
                @click="handleGmailFilter('inbox')"
              >
                <Icon name="solar:inbox-linear" class="size-5" />
              </BaseButton>
            </BaseTooltip>
          </div>
          <div class="flex h-12 w-full items-center justify-center shrink-0">
            <BaseTooltip
              content="Sent"
              variant="dark"
              :bindings="{
                content: { side: 'left' },
                portal: { disabled: true },
              }"
            >
              <BaseButton
                size="icon-sm"
                rounded="md"
                variant="ghost"
                :class="{ 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400': route.query.filter === 'sent' }"
                @click="handleGmailFilter('sent')"
              >
                <Icon name="solar:inbox-out-linear" class="size-5" />
              </BaseButton>
            </BaseTooltip>
          </div>
          <div class="flex h-12 w-full items-center justify-center shrink-0">
            <BaseTooltip
              content="Important"
              variant="dark"
              :bindings="{
                content: { side: 'left' },
                portal: { disabled: true },
              }"
            >
              <BaseButton
                size="icon-sm"
                rounded="md"
                variant="ghost"
                :class="{ 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400': route.query.filter === 'important' }"
                @click="handleGmailFilter('important')"
              >
                <Icon name="solar:bookmark-linear" class="size-5" />
              </BaseButton>
            </BaseTooltip>
          </div>
          <div class="flex h-12 w-full items-center justify-center shrink-0">
            <BaseTooltip
              content="Spam"
              variant="dark"
              :bindings="{
                content: { side: 'left' },
                portal: { disabled: true },
              }"
            >
              <BaseButton
                size="icon-sm"
                rounded="md"
                variant="ghost"
                :class="{ 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400': route.query.filter === 'spam' }"
                @click="handleGmailFilter('spam')"
              >
                <Icon name="solar:trash-bin-trash-linear" class="size-5" />
              </BaseButton>
            </BaseTooltip>
          </div>
          <div class="flex h-12 w-full items-center justify-center shrink-0">
            <BaseTooltip
              content="Calendar"
              variant="dark"
              :bindings="{
                content: { side: 'left' },
                portal: { disabled: true },
              }"
            >
              <BaseButton
                size="icon-sm"
                rounded="md"
                variant="ghost"
                :class="{ 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400': route.path.startsWith('/calendar') }"
                @click="handleCalendarNavigation()"
              >
                <Icon name="solar:calendar-linear" class="size-5" />
              </BaseButton>
            </BaseTooltip>
          </div>
        </TairoSidebarSubsidebarContent>
      </TairoSidebarSubsidebar>

      <TairoSidebarSubsidebar value="Calendar" class="flex flex-col">
        <TairoSidebarSubsidebarContent>
          <!-- Calendar Header with Actions -->
          <div class="flex items-center justify-between px-4 py-3 border-b border-muted-200 dark:border-muted-800">
            <span class="text-sm font-medium text-muted-900 dark:text-muted-100">Calendars</span>
            <div class="flex items-center gap-1">
              <BaseTooltip
                content="Create Calendar"
                variant="dark"
                :bindings="{
                  content: { side: 'left' },
                  portal: { disabled: true },
                }"
              >
                <BaseButton
                  size="icon-xs"
                  rounded="md"
                  variant="ghost"
                  @click="openCalendarCreationModal"
                >
                  <Icon name="lucide:calendar-plus" class="size-4" />
                </BaseButton>
              </BaseTooltip>
              <BaseTooltip
                content="Integrations"
                variant="dark"
                :bindings="{
                  content: { side: 'left' },
                  portal: { disabled: true },
                }"
              >
                <BaseButton
                  size="icon-xs"
                  rounded="md"
                  variant="ghost"
                  @click="showIntegrationModal = true"
                >
                  <Icon name="lucide:settings" class="size-4" />
                </BaseButton>
              </BaseTooltip>
            </div>
          </div>

          <!-- Calendar List -->
          <div class="overflow-y-auto nui-slimscroll" style="max-height: calc(100vh - 200px)">
            <!-- Loading State -->
            <div v-if="calendarsLoading" class="px-4 py-8">
              <div class="flex items-center justify-center gap-2">
                <Icon name="svg-spinners:90-ring" class="size-5 text-primary-500" />
                <span class="text-sm text-muted-600 dark:text-muted-400">Loading calendars...</span>
              </div>
            </div>

            <!-- Calendar Items -->
            <div v-else-if="calendars.length > 0" class="py-2">
              <BaseRadioGroup
                v-model="selectedCalendarId"
                name="calendar-selection"
              >
                <!-- Show All Calendars Option -->
                <div
                  class="group relative flex items-center gap-3 px-4 py-2 cursor-pointer hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
                  :class="{ 'bg-primary-100 dark:bg-primary-900/20': selectedCalendarId === 'all' }"
                >
                  <BaseRadio
                    name="calendar-selection"
                    value="all"
                  />
                  <span class="text-sm font-medium text-muted-900 dark:text-muted-100">Show All Calendars</span>
                </div>

                <!-- Individual Calendars -->
                <div
                  v-for="calendar in calendars"
                  :key="calendar.id"
                  class="group relative flex items-center gap-3 px-4 py-2 cursor-pointer hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
                  :class="{ 'bg-primary-100 dark:bg-primary-900/20': selectedCalendarId === calendar.id }"
                >
                  <!-- Radio Button -->
                  <BaseRadio
                    name="calendar-selection"
                    :value="calendar.id"
                  />

                  <!-- Calendar Color Dot -->
                  <BaseTooltip
                    :content="calendar.name"
                    variant="dark"
                    :bindings="{
                      content: { side: 'left' },
                      portal: { disabled: true },
                    }"
                  >
                    <div
                      class="size-3 rounded-full shrink-0"
                      :style="{ backgroundColor: calendar.color }"
                    />
                  </BaseTooltip>

                  <!-- Calendar Name -->
                  <span class="text-sm text-muted-700 dark:text-muted-300 truncate flex-1">{{ calendar.name }}</span>

                  <!-- Icons -->
                  <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <!-- Default Star -->
                    <BaseTooltip
                      v-if="calendar.isDefault"
                      content="Default Calendar"
                      variant="dark"
                      :bindings="{
                        content: { side: 'left' },
                        portal: { disabled: true },
                      }"
                    >
                      <Icon name="lucide:star" class="size-3 text-warning-500" />
                    </BaseTooltip>

                    <!-- Google Calendar Icon -->
                    <BaseTooltip
                      v-if="calendar.provider === 'google'"
                      content="Google Calendar"
                      variant="dark"
                      :bindings="{
                        content: { side: 'left' },
                        portal: { disabled: true },
                      }"
                    >
                      <Icon name="logos:google-icon" class="size-3" />
                    </BaseTooltip>

                    <!-- Visibility Toggle -->
                    <BaseTooltip
                      :content="calendar.isVisible ? 'Hide Calendar' : 'Show Calendar'"
                      variant="dark"
                      :bindings="{
                        content: { side: 'left' },
                        portal: { disabled: true },
                      }"
                    >
                      <BaseButton
                        size="icon-xs"
                        rounded="md"
                        variant="ghost"
                        @click.stop="toggleCalendarVisibility(calendar.id)"
                      >
                        <Icon :name="calendar.isVisible ? 'lucide:eye' : 'lucide:eye-off'" class="size-3" />
                      </BaseButton>
                    </BaseTooltip>

                    <!-- More Options -->
                    <BaseDropdown>
                      <template #trigger>
                        <BaseButton
                          size="icon-xs"
                          rounded="md"
                          variant="ghost"
                          @click.stop
                        >
                          <Icon name="lucide:more-vertical" class="size-3" />
                        </BaseButton>
                      </template>
                      <BaseDropdownItem @click="openCalendarEditModal(calendar)">
                        <Icon name="lucide:edit" class="size-4" />
                        <span>Edit</span>
                      </BaseDropdownItem>
                      <BaseDropdownItem @click="() => setDefaultCalendar(calendar.id)">
                        <Icon name="lucide:star" class="size-4" />
                        <span>Set as Default</span>
                      </BaseDropdownItem>
                      <hr class="my-1 border-t border-gray-200 dark:border-gray-700" />
                      <BaseDropdownItem color="danger" @click="() => handleDeleteCalendar(calendar.id)">
                        <Icon name="lucide:trash" class="size-4" />
                        <span>Delete</span>
                      </BaseDropdownItem>
                    </BaseDropdown>
                  </div>
                </div>
              </BaseRadioGroup>
            </div>

            <!-- Empty State -->
            <div v-else class="px-4 py-8">
              <div class="text-center">
                <Icon name="lucide:calendar-x" class="size-8 text-muted-400 dark:text-muted-600 mx-auto mb-3" />
                <p class="text-sm text-muted-600 dark:text-muted-400 mb-4">
                  No calendars yet
                </p>
                <BaseButton
                  size="sm"
                  variant="soft"
                  color="primary"
                  @click="openCalendarCreationModal"
                >
                  <Icon name="lucide:plus" class="size-4" />
                  <span>Create Calendar</span>
                </BaseButton>
              </div>
            </div>
          </div>

          <!-- Quick Actions Footer -->
          <div class="border-t border-muted-200 dark:border-muted-800 px-4 py-3 mt-auto">
            <div v-if="isGoogleCalendarConnected" class="flex items-center justify-between mb-2">
              <span class="text-xs text-muted-600 dark:text-muted-400">Google Calendar</span>
              <BaseButton
                size="xs"
                variant="ghost"
                color="primary"
                :loading="isGoogleCalendarConnecting"
                @click="handleSyncGoogleCalendar"
              >
                <Icon name="lucide:refresh-cw" class="size-3" />
                <span>Sync</span>
              </BaseButton>
            </div>
            <BaseButton
              v-else
              size="sm"
              variant="soft"
              color="primary"
              class="w-full"
              :loading="isGoogleCalendarConnecting"
              @click="handleConnectGoogleCalendar"
            >
              <Icon name="logos:google-icon" class="size-4" />
              <span>Connect Google Calendar</span>
            </BaseButton>
          </div>
        </TairoSidebarSubsidebarContent>
      </TairoSidebarSubsidebar>
    </TairoSidebarNav>

    <TairoSidebarContent class="min-h-screen">
      <div class="px-4 md:px-6 xl:pe-4 xl:ps-6">
        <Toolbar
          @toggle-mobile-nav="toggleMobileNav"
        />
      </div>
      <slot />
    </TairoSidebarContent>
  </TairoSidebarLayout>

  <!-- Calendar Creation Modal -->
  <TairoModal
    v-model="showCalendarCreationModal"
    size="md"
    :title="calendarToEdit ? 'Edit Calendar' : 'Create Calendar'"
  >
    <form id="calendar-form" @submit.prevent="calendarToEdit ? handleUpdateCalendar() : handleCreateCalendar()">
      <div class="space-y-4">
        <!-- Calendar Name -->
        <BaseField label="Calendar Name" required>
          <BaseInput
            v-model="newCalendarForm.name"
            placeholder="e.g., Work Calendar"
            required
          />
        </BaseField>

        <!-- Calendar Description -->
        <BaseField label="Description">
          <BaseTextarea
            v-model="newCalendarForm.description"
            placeholder="Optional description for this calendar"
            rows="3"
          />
        </BaseField>

        <!-- Calendar Color -->
        <BaseField label="Color">
          <div class="grid grid-cols-4 gap-2">
            <BaseTooltip
              v-for="color in calendarColors"
              :key="color.value"
              :content="color.name"
              variant="dark"
            >
              <button
                type="button"
                class="size-10 rounded-lg border-2 transition-all"
                :class="[
                  newCalendarForm.color === color.value
                    ? 'border-primary-500 dark:border-primary-400 ring-2 ring-primary-500/20'
                    : 'border-transparent',
                ]"
                :style="{ backgroundColor: color.value }"
                @click="newCalendarForm.color = color.value"
              />
            </BaseTooltip>
          </div>
        </BaseField>

        <!-- Default Calendar -->
        <BaseField>
          <BaseSwitchThin
            v-model="newCalendarForm.isDefault"
            label="Set as default calendar"
          />
        </BaseField>

        <!-- Google Calendar Link (if connected) -->
        <BaseField v-if="isGoogleCalendarConnected && !calendarToEdit">
          <BaseSwitchThin
            v-model="newCalendarForm.linkToGoogle"
            label="Link to Google Calendar"
          />
        </BaseField>
      </div>
    </form>

    <template #footer>
      <div class="flex gap-2">
        <BaseButton
          variant="ghost"
          @click="showCalendarCreationModal = false"
        >
          Cancel
        </BaseButton>
        <BaseButton
          type="submit"
          form="calendar-form"
          color="primary"
          variant="solid"
        >
          {{ calendarToEdit ? 'Update' : 'Create' }}
        </BaseButton>
      </div>
    </template>
  </TairoModal>

  <!-- Integration Management Modal -->
  <TairoModal
    v-model="showIntegrationModal"
    size="md"
    title="Calendar Integrations"
  >
    <div class="space-y-4">
      <!-- Google Calendar -->
      <div class="flex items-center justify-between p-4 rounded-lg border border-muted-200 dark:border-muted-800">
        <div class="flex items-center gap-3">
          <Icon name="logos:google-icon" class="size-8" />
          <div>
            <h4 class="font-medium text-muted-900 dark:text-muted-100">
              Google Calendar
            </h4>
            <p class="text-sm text-muted-600 dark:text-muted-400">
              {{ isGoogleCalendarConnected ? 'Connected' : 'Not connected' }}
            </p>
          </div>
        </div>
        <BaseButton
          v-if="isGoogleCalendarConnected"
          size="sm"
          variant="ghost"
          color="danger"
          @click="disconnectGoogleCalendar"
        >
          Disconnect
        </BaseButton>
        <BaseButton
          v-else
          size="sm"
          variant="solid"
          color="primary"
          :loading="isGoogleCalendarConnecting"
          @click="handleConnectGoogleCalendar"
        >
          Connect
        </BaseButton>
      </div>

      <!-- Outlook Calendar (Coming Soon) -->
      <div class="flex items-center justify-between p-4 rounded-lg border border-muted-200 dark:border-muted-800 opacity-50">
        <div class="flex items-center gap-3">
          <Icon name="logos:microsoft-icon" class="size-8" />
          <div>
            <h4 class="font-medium text-muted-900 dark:text-muted-100">
              Outlook Calendar
            </h4>
            <p class="text-sm text-muted-600 dark:text-muted-400">
              Coming soon
            </p>
          </div>
        </div>
        <BaseButton size="sm" variant="ghost" disabled>
          Connect
        </BaseButton>
      </div>

      <!-- Apple Calendar (Coming Soon) -->
      <div class="flex items-center justify-between p-4 rounded-lg border border-muted-200 dark:border-muted-800 opacity-50">
        <div class="flex items-center gap-3">
          <Icon name="logos:apple" class="size-8" />
          <div>
            <h4 class="font-medium text-muted-900 dark:text-muted-100">
              Apple Calendar
            </h4>
            <p class="text-sm text-muted-600 dark:text-muted-400">
              Coming soon
            </p>
          </div>
        </div>
        <BaseButton size="sm" variant="ghost" disabled>
          Connect
        </BaseButton>
      </div>
    </div>

    <template #footer>
      <BaseButton variant="ghost" @click="showIntegrationModal = false">
        Close
      </BaseButton>
    </template>
  </TairoModal>
</template>
