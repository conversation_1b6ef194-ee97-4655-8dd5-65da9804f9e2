# MCP Testing Task

## Overview
Use MCP tools for comprehensive testing, including unit test generation, web UI testing with <PERSON><PERSON>, and test quality validation.

## Available MCP Testing Tools

### Test Generation
- **`*mcp-testgen`** - Generate comprehensive test suites with edge cases
- **`*mcp-codereview`** - Review test quality and coverage
- **`*mcp-analyze`** - Analyze code for testability

### Playwright Web Testing (Requires Installation)
- **`*mcp-playwright navigate`** - Navigate to URLs
- **`*mcp-playwright click`** - Click elements
- **`*mcp-playwright fill`** - Fill form fields
- **`*mcp-playwright screenshot`** - Capture screenshots
- **`*mcp-playwright assert`** - Validate conditions
- **`*mcp-playwright wait`** - Wait for page states
- **`*mcp-playwright evaluate`** - Execute JavaScript

## Installation

### Installing Playwright MCP
```bash
# Run in project root
npx @mcpintegrations/install playwright

# This adds Playwright MCP to <PERSON>'s configuration
# Restart Claude after installation
```

## Task Execution Patterns

### Pattern 1: Comprehensive Test Generation
```markdown
# QA creating test suite for new feature
*QA: "Generating comprehensive tests for payment module..."

# Step 1: Analyze code for test generation
*mcp-analyze "/Users/<USER>/src/payment/"
→ Identifies critical paths and edge cases

# Step 2: Generate test suite
*mcp-testgen "Payment processing with multiple currencies"
→ Creates unit tests, integration tests, edge cases

# Step 3: Review test quality
*mcp-codereview "/Users/<USER>/tests/payment/"
→ Validates test coverage and quality
```

### Pattern 2: E2E Web Testing with Playwright
```markdown
# QA testing e-commerce checkout flow
*QA: "Running Playwright tests for checkout..."

# Navigate to store
*mcp-playwright navigate "https://store.example.com"

# Search for product
*mcp-playwright fill "#search-input" "laptop"
*mcp-playwright click "#search-button"
*mcp-playwright wait "div.product-results"

# Add to cart
*mcp-playwright click "[data-testid='add-to-cart-button']:first"
*mcp-playwright wait "div.cart-notification"

# Go to checkout
*mcp-playwright navigate "/checkout"
*mcp-playwright screenshot "checkout-page"

# Fill checkout form
*mcp-playwright fill "#email" "<EMAIL>"
*mcp-playwright fill "#card-number" "****************"
*mcp-playwright fill "#card-expiry" "12/25"
*mcp-playwright fill "#card-cvc" "123"

# Complete order
*mcp-playwright click "#submit-order"
*mcp-playwright wait "div.order-confirmation"
*mcp-playwright assert "text=Order Confirmed"
*mcp-playwright screenshot "order-confirmation"
```

### Pattern 3: Visual Regression Testing
```markdown
# QA checking UI changes
*QA: "Running visual regression tests..."

# Capture baseline screenshots
*mcp-playwright navigate "/dashboard"
*mcp-playwright screenshot "dashboard-baseline"

*mcp-playwright navigate "/profile"
*mcp-playwright screenshot "profile-baseline"

# After UI changes, capture new screenshots
*mcp-playwright navigate "/dashboard"
*mcp-playwright screenshot "dashboard-updated"

*mcp-playwright navigate "/profile"
*mcp-playwright screenshot "profile-updated"

# Compare screenshots for visual differences
```

### Pattern 4: Form Validation Testing
```markdown
# QA testing form validations
*QA: "Testing registration form validations..."

# Test empty form submission
*mcp-playwright navigate "/register"
*mcp-playwright click "#submit-button"
*mcp-playwright assert "text=Email is required"
*mcp-playwright assert "text=Password is required"

# Test invalid email
*mcp-playwright fill "#email" "invalid-email"
*mcp-playwright click "#submit-button"
*mcp-playwright assert "text=Please enter a valid email"

# Test weak password
*mcp-playwright fill "#email" "<EMAIL>"
*mcp-playwright fill "#password" "123"
*mcp-playwright click "#submit-button"
*mcp-playwright assert "text=Password must be at least 8 characters"

# Test successful registration
*mcp-playwright fill "#password" "SecurePass123!"
*mcp-playwright click "#submit-button"
*mcp-playwright wait "div.welcome-message"
*mcp-playwright assert "text=Registration successful"
```

## Integration with PIB Workflows

### QA Agent Testing Workflow
```markdown
*QA create-test-plan "user authentication"
  → Uses *mcp-testgen for unit test creation
  → Uses *mcp-playwright for E2E testing
  → Uses *mcp-codereview for test quality

*QA run-tests
  → Executes generated unit tests
  → Runs Playwright E2E tests
  → Captures screenshots and results
```

### Dev Integration Testing
```markdown
*Dev implement "shopping cart"
  → Implementation complete
  → *mcp-testgen "shopping cart edge cases"
  → *mcp-playwright for UI interaction tests
  → Validates with generated tests
```

## Best Practices

### Test Generation Best Practices
1. **Analyze First**: Use `*mcp-analyze` before generating tests
2. **Edge Cases**: Always request edge case coverage
3. **Test Patterns**: Follow existing test patterns in codebase
4. **Review Tests**: Use `*mcp-codereview` on generated tests

### Playwright Testing Best Practices
1. **Wait for Elements**: Always wait for elements before interaction
2. **Use Test IDs**: Prefer `data-testid` attributes over CSS selectors
3. **Screenshot Key States**: Capture screenshots at critical points
4. **Clean Test Data**: Ensure tests don't pollute production data
5. **Parallel Execution**: Design tests to run independently

## Test Organization

### Generated Test Structure
```
tests/
├── unit/
│   ├── payment/
│   │   ├── payment-processor.test.js
│   │   ├── currency-converter.test.js
│   │   └── payment-validation.test.js
│   └── auth/
│       ├── login.test.js
│       └── session.test.js
├── integration/
│   ├── payment-flow.test.js
│   └── auth-flow.test.js
└── e2e/
    ├── checkout.spec.js
    ├── registration.spec.js
    └── screenshots/
        ├── baseline/
        └── current/
```

### MCP Session Logs
```
.ai/mcp-sessions/
├── 2024-06-24-testgen-payment/
│   ├── analysis.md
│   ├── generated-tests.json
│   └── coverage-report.md
└── 2024-06-24-playwright-checkout/
    ├── test-script.js
    ├── screenshots/
    │   ├── checkout-page.png
    │   └── order-confirmation.png
    └── results.json
```

## Common Testing Scenarios

### 1. New Feature Testing
```markdown
1. *mcp-analyze [feature-code]
2. *mcp-testgen [feature-description]
3. *mcp-playwright [ui-flow-tests]
4. *mcp-codereview [generated-tests]
```

### 2. Regression Testing
```markdown
1. *mcp-playwright navigate [baseline-urls]
2. *mcp-playwright screenshot [baseline-captures]
3. Deploy changes
4. *mcp-playwright screenshot [comparison-captures]
5. Compare results
```

### 3. Performance Testing
```markdown
1. *mcp-playwright evaluate "performance.timing"
2. *mcp-analyze [performance-metrics]
3. *mcp-testgen [performance-test-suite]
```

### 4. Accessibility Testing
```markdown
1. *mcp-playwright evaluate "axe.run()"
2. Analyze accessibility violations
3. *mcp-testgen [accessibility-test-cases]
```

## Troubleshooting

### Common Issues
1. **Playwright Not Found**: Run `npx @mcpintegrations/install playwright`
2. **Timeout Errors**: Increase wait times or check selectors
3. **Screenshot Failures**: Ensure viewport size is set correctly
4. **Test Flakiness**: Add proper waits and error handling

### Debug Commands
```markdown
# Check if element exists
*mcp-playwright evaluate "document.querySelector('#element-id')"

# Get page URL
*mcp-playwright evaluate "window.location.href"

# Check console errors
*mcp-playwright evaluate "window.consoleErrors"
```
