# Enhanced Machine-Powered Capabilities Configuration
# This file defines external tools and services available to PIB agents
# Updated to leverage Claude Code's latest MCP integration features

# === CLAUDE CODE INTEGRATION NOTES ===
# - MCP tools follow format: mcp__$serverName__$toolName
# - Use --allowedTools flag to control access per agent context
# - hook_event_name parameter now available for context-aware operations
# - Supports timeout configuration and tilde (~) expansion

available_mpcs:
  search:
    - name: Perplexity
      description: Web search with summarization capabilities
      best_for: [Research, Market analysis, Technology trends]
      command: '*perplexity'
      agents: [Analyst, PM, PIB]

  code_search:
    - name: GitHub
      description: Search code repositories and documentation
      best_for: [Code examples, Library documentation, Implementation patterns]
      command: '*github'
      agents: [<PERSON><PERSON><PERSON>, <PERSON>, DevOps]

  web_scraping:
    - name: Firecrawl
      description: Web scraping and content extraction
      best_for: [Competitor analysis, Market intelligence, Content extraction]
      command: '*firecrawl'
      agents: [<PERSON><PERSON><PERSON>, Research Agent, PM]

  multi_model_analysis:
    - name: Zen
      description: Multi-model AI development assistance
      best_for: [Code analysis, Deep thinking, Multi-model validation]
      command: '*zen'
      agents: [<PERSON>ly<PERSON>, <PERSON>, Developer, Q<PERSON>]

  browser_automation:
    - name: Playwright
      description: Browser automation and testing
      best_for: [Automated testing, User flow testing, Browser interaction]
      command: '*playwright'
      agents: [QA, Tester, Developer]

  documentation:
    - name: Context7
      description: Library and framework documentation retrieval
      best_for: [API documentation, Framework research, Technical implementation]
      command: '*context7'
      agents: [Analyst, Developer, Architect]
      claude_code_integration:
        mcp_format: mcp__context7__get_library_docs
        allowed_tools: [mcp__context7__resolve_library_id, mcp__context7__get_library_docs]
        usage_example: 'claude -p "Get React hooks documentation" --mcp-config mcp-servers.json --allowedTools "mcp__context7__get_library_docs"'

# === CLAUDE CODE MCP CONFIGURATION PATTERNS ===
claude_code_integration:
  mcp_servers_config: |
    {
      "mcpServers": {
        "context7": {
          "command": "npx",
          "args": ["-y", "@context7/mcp-server"],
          "env": {
            "CONTEXT7_API_KEY": "configured"
          }
        },
        "firecrawl": {
          "command": "npx",
          "args": ["-y", "@firecrawl/mcp-server"],
          "env": {
            "FIRECRAWL_API_KEY": "configured"
          }
        },
        "zen": {
          "command": "python",
          "args": ["pib-agent/mcp-server/pib_mcp_server.py"],
          "env": {}
        },
        "perplexity": {
          "command": "npx",
          "args": ["-y", "@perplexity/mcp-server"],
          "env": {
            "PERPLEXITY_API_KEY": "configured"
          }
        },
        "playwright": {
          "command": "npx",
          "args": ["-y", "@playwright/mcp-server"],
          "env": {}
        }
      }
    }

  # Agent-specific MCP tool access control
  agent_tool_mapping:
    Research Agent:
      allowed_tools: [mcp__perplexity__search, mcp__firecrawl__scrape, mcp__context7__get_library_docs]
      scope: local
    Architect Agent:
      allowed_tools: [mcp__zen__thinkdeep, mcp__zen__consensus, mcp__context7__get_library_docs, mcp__zen__analyze]
      scope: project
    Developer Agent:
      allowed_tools: [mcp__zen__debug, mcp__zen__testgen, mcp__context7__get_library_docs, mcp__zen__codereview, mcp__zen__chat, mcp__zen__analyze]
      scope: project
    QA Tester Agent:
      allowed_tools:
        # Full Playwright MCP Integration
        - mcp__playwright__navigate
        - mcp__playwright__click
        - mcp__playwright__fill
        - mcp__playwright__select
        - mcp__playwright__hover
        - mcp__playwright__drag
        - mcp__playwright__press_key
        - mcp__playwright__upload_file
        - mcp__playwright__screenshot
        - mcp__playwright__get_visible_text
        - mcp__playwright__get_visible_html
        - mcp__playwright__evaluate
        - mcp__playwright__console_logs
        - mcp__playwright__close
        - mcp__playwright__get
        - mcp__playwright__post
        - mcp__playwright__put
        - mcp__playwright__patch
        - mcp__playwright__delete
        - mcp__playwright__expect_response
        - mcp__playwright__assert_response
        - mcp__playwright__custom_user_agent
        - mcp__playwright__go_back
        - mcp__playwright__go_forward
        - mcp__playwright__save_as_pdf
        - mcp__playwright__click_and_switch_tab
        - mcp__playwright__iframe_click
        - mcp__playwright__iframe_fill
        # Zen MCP Testing Tools
        - mcp__zen__testgen
        - mcp__zen__codereview
        - mcp__zen__analyze
        - mcp__zen__debug

      scope: project
      enhanced_testing:
        credential_management: true
        session_persistence: true
        multi_browser_support: true
        visual_regression: true
        performance_monitoring: true
        accessibility_testing: true
    Dev Orchestrator:
      allowed_tools: [mcp__zen__chat, mcp__zen__thinkdeep, mcp__zen__consensus, mcp__context7__get_library_docs, mcp__zen__analyze]
      scope: orchestration
    Context Engineering:
      allowed_tools: [mcp__zen__chat, mcp__zen__analyze, mcp__context7__resolve_library_id, mcp__context7__get_library_docs]
      scope: context

  # Hook integration with MCP events
  hook_integration:
    PostToolUse:
      context_aware: true
      agent_validation: true
      mcp_tool_logging: true
    SubagentStop:
      context_aware: true
      agent_transition: true
    Stop:
      context_aware: true
      cleanup_mcp_connections: true

  # === DEV WORKFLOW SPECIFIC CONFIGURATIONS ===
  dev_workflow_integration:
    context_engineering:
      zen_analysis_patterns:
        requirement_analysis:
          tools: [mcp__zen__chat, mcp__zen__thinkdeep]
          focus: requirement clarification and structuring
          output: structured requirements with acceptance criteria
        complexity_assessment:
          tools: [mcp__zen__analyze, mcp__zen__consensus]
          focus: technical complexity and implementation approaches
          output: complexity score and implementation recommendations
        risk_analysis:
          tools: [mcp__zen__thinkdeep, mcp__zen__chat]
          focus: technical risks and mitigation strategies
          output: risk assessment with mitigation plans

      context7_research_patterns:
        pattern_research:
          tools: [mcp__context7__resolve_library_id, mcp__context7__get_library_docs]
          focus: existing implementation patterns and solutions
          output: pattern analysis and applicability assessment
        library_analysis:
          tools: [mcp__context7__get_library_docs]
          focus: 'relevant libraries, frameworks, and tools'
          output: library recommendations with pros/cons analysis
        best_practices:
          tools: [mcp__context7__get_library_docs]
          focus: industry best practices and standards
          output: best practices guide and implementation recommendations

    agent_enhancement:
      james_developer:
        pre_implementation:
          - tool: mcp__context7__get_library_docs
            purpose: API documentation and framework guidance
            trigger: before implementing new functionality
          - tool: mcp__zen__chat
            purpose: technical brainstorming and problem solving
            trigger: when facing complex technical decisions

        during_implementation:
          - tool: mcp__zen__debug
            purpose: systematic debugging for complex issues
            trigger: when debugging attempts exceed 3 iterations
          - tool: mcp__context7__get_library_docs
            purpose: just-in-time documentation lookup
            trigger: when integrating with libraries or frameworks

        quality_validation:
          - tool: mcp__zen__codereview
            purpose: multi-model code review and validation
            trigger: before marking tasks complete
          - tool: mcp__zen__analyze
            purpose: impact assessment and optimization opportunities
            trigger: before final review submission

    workflow_automation:
      dev_command_triggers:
        simple_tasks:
          complexity_threshold: 3
          agent_assignment: single-agent
          mcp_tools: [context7, zen-basic]
          quality_gates: [standard-review, pattern-validation]

        complex_tasks:
          complexity_threshold: 7
          agent_assignment: enhanced-single-agent
          mcp_tools: [context7, zen-comprehensive]
          quality_gates: [enhanced-review, architect-validation, security-check]

        large_features:
          complexity_threshold: 10
          agent_assignment: multi-agent
          mcp_tools: [context7, zen-full, additional-tools]
          quality_gates: [multi-reviewer, integration-testing, performance-validation]

    quality_enhancement:
      lever_framework_validation:
        leverage_detection:
          tools: [mcp__context7__get_library_docs, mcp__zen__analyze]
          validation: identify existing patterns and reusable components
          automation: automatic pattern detection and recommendation

        extend_opportunities:
          tools: [mcp__zen__analyze, mcp__context7__get_library_docs]
          validation: identify extension points in existing functionality
          automation: extension point analysis and implementation guidance

        verify_continuous:
          tools: [mcp__zen__codereview, mcp__zen__analyze]
          validation: continuous verification during development
          automation: automated quality gate validation

        eliminate_duplication:
          tools: [mcp__zen__analyze, mcp__zen__codereview]
          validation: detect and eliminate code duplication
          automation: duplication detection and consolidation recommendations

        reduce_complexity:
          tools: [mcp__zen__analyze, mcp__zen__thinkdeep]
          validation: identify and reduce unnecessary complexity
          automation: complexity analysis and simplification recommendations

  # === ENHANCED E2E TESTING CONFIGURATION ===
  enhanced_testing:
    default_configuration:
      browser_mode: headless # headless | visible
      default_port: 3000
      auto_port_detection: true
      timeout_settings:
        page_load: 30000
        element_wait: 10000
        test_execution: 300000

    credential_management:
      storage_location: .ai/testing/credentials.encrypted
      encryption_enabled: true
      session_persistence: true
      auto_login: true

    testing_modes:
      headless:
        description: Fast automated testing for CI/CD
        use_cases: [regression, automation, performance]
        browser_args: [--headless, --disable-gpu, --no-sandbox]

      visible:
        description: Visual debugging mode for test development
        use_cases: [debugging, development, manual_validation]
        browser_args: [--no-sandbox]

    storage_configuration:
      screenshots: .ai/testing/screenshots/
      test_reports: .ai/testing/reports/
      session_data: .ai/testing/sessions/
      performance_logs: .ai/testing/performance/

    testing_capabilities:
      visual_regression:
        enabled: true
        baseline_storage: .ai/testing/baselines/
        comparison_threshold: 0.1

      performance_testing:
        enabled: true
        metrics: [page_load, first_contentful_paint, largest_contentful_paint]
        thresholds:
          page_load: 3000
          first_contentful_paint: 1500

      accessibility_testing:
        enabled: true
        standards: [WCAG2.1, Section508]
        severity_threshold: medium

      cross_browser_testing:
        enabled: true
        browsers: [chromium, firefox, webkit]
        parallel_execution: false

    command_mapping:
      test_headless:
        mcp_tools: [mcp__playwright__navigate, mcp__playwright__screenshot, mcp__playwright__console_logs]
        mode: headless
        reporting: automated

      test_visible:
        mcp_tools: [mcp__playwright__navigate, mcp__playwright__screenshot, mcp__playwright__console_logs]
        mode: visible
        reporting: interactive

      test_performance:
        mcp_tools: [mcp__playwright__evaluate, mcp__playwright__console_logs]
        metrics_collection: true
        performance_analysis: true

      test_accessibility:
        mcp_tools: [mcp__playwright__evaluate, mcp__playwright__get_visible_html]
        accessibility_audit: true
        compliance_validation: true

  # Add other categories and tools as needed
  image_generation:
    - name: DALL-E
      description: AI image generation for mockups and concepts
      best_for: [UI mockups, Concept visualization]
      command: '*dalle'
      agents: [Design Architect, PM]

  # === DEV WORKFLOW COMMAND INTEGRATION ===
  dev_command_patterns:
    usage_examples:
      simple_feature: '/dev "add password reset functionality"'
      complex_feature: '/dev "implement real-time chat system with message persistence" --priority=high --focus=performance'
      api_development: '/dev "create RESTful API for user management" --style=comprehensive'
      security_feature: '/dev "implement OAuth 2.0 authentication" --focus=security --priority=high'
      ui_component: '/dev "create responsive dashboard component" --focus=ui --style=standard'

    parameter_mapping:
      priority_levels:
        low: 'minimal quality gates, basic MCP tool usage'
        medium: 'standard quality gates, comprehensive MCP tool usage'
        high: 'enhanced quality gates, full MCP tool integration'
        critical: 'maximum quality gates, expert validation, comprehensive analysis'

      focus_areas:
        security: 'security-focused analysis, OWASP compliance, security testing'
        performance: 'performance optimization, benchmarking, scalability analysis'
        ui: 'user experience focus, accessibility compliance, design system integration'
        api: 'API design patterns, OpenAPI compliance, comprehensive documentation'
        data: 'data integrity, query optimization, migration strategies'
        general: balanced approach across all quality dimensions

      style_preferences:
        minimal: 'basic implementation, essential features only'
        standard: balanced implementation with good practices
        comprehensive: full-featured implementation with extensive documentation and testing
