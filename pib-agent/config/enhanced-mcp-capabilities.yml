# Enhanced MCP Capabilities Configuration
# Dynamic tool selection and context-aware MCP integration

version: '2.0'
last_updated: 2025-01-04

# Tool effectiveness ratings and context mappings
tool_effectiveness:
  zen:
    tools:
      chat:
        effectiveness: 0.9
        contexts: [brainstorming, collaboration, problem-solving]
        complexity_suitability: [simple, medium, complex, expert]
        domains: [general, code, architecture, research]

      thinkdeep:
        effectiveness: 0.95
        contexts: [complex-analysis, investigation, validation]
        complexity_suitability: [complex, expert]
        domains: [architecture, debugging, security]

      analyze:
        effectiveness: 0.85
        contexts: [code-review, assessment, evaluation]
        complexity_suitability: [medium, complex, expert]
        domains: [code, architecture, quality]

      debug:
        effectiveness: 0.9
        contexts: [troubleshooting, issue-resolution, root-cause]
        complexity_suitability: [medium, complex, expert]
        domains: [code, debugging]

      codereview:
        effectiveness: 0.88
        contexts: [quality-assurance, standards-validation, improvement]
        complexity_suitability: [medium, complex, expert]
        domains: [code, quality, security]

      refactor:
        effectiveness: 0.82
        contexts: [improvement, optimization, modernization]
        complexity_suitability: [medium, complex, expert]
        domains: [code, architecture]

      secaudit:
        effectiveness: 0.87
        contexts: [security-review, compliance, vulnerability-assessment]
        complexity_suitability: [medium, complex, expert]
        domains: [security, compliance]

      testgen:
        effectiveness: 0.83
        contexts: [testing, validation, coverage]
        complexity_suitability: [medium, complex, expert]
        domains: [testing, quality]

      planner:
        effectiveness: 0.86
        contexts: [planning, strategy, roadmap]
        complexity_suitability: [medium, complex, expert]
        domains: [planning, architecture, management]

      consensus:
        effectiveness: 0.91
        contexts: [decision-making, validation, agreement]
        complexity_suitability: [complex, expert]
        domains: [architecture, management, quality]

      docgen:
        effectiveness: 0.84
        contexts: [documentation, knowledge-capture]
        complexity_suitability: [simple, medium, complex]
        domains: [documentation, knowledge]

      precommit:
        effectiveness: 0.87
        contexts: [validation, quality-gates, compliance]
        complexity_suitability: [medium, complex, expert]
        domains: [quality, process]

  context7:
    tools:
      resolve-library-id:
        effectiveness: 0.92
        contexts: [research, documentation, library-discovery]
        complexity_suitability: [simple, medium, complex, expert]
        domains: [research, documentation, development]

      get-library-docs:
        effectiveness: 0.94
        contexts: [implementation, reference, best-practices]
        complexity_suitability: [simple, medium, complex, expert]
        domains: [research, documentation, development]

  perplexity:
    tools:
      search:
        effectiveness: 0.88
        contexts: [research, market-analysis, trend-discovery]
        complexity_suitability: [simple, medium, complex, expert]
        domains: [research, market, general]

      get_documentation:
        effectiveness: 0.85
        contexts: [technology-research, framework-evaluation]
        complexity_suitability: [medium, complex, expert]
        domains: [research, technology, documentation]

  firecrawl:
    tools:
      scrape:
        effectiveness: 0.83
        contexts: [data-extraction, competitive-analysis, research]
        complexity_suitability: [medium, complex, expert]
        domains: [research, competitive, data]

      search:
        effectiveness: 0.86
        contexts: [information-discovery, competitive-research]
        complexity_suitability: [medium, complex, expert]
        domains: [research, competitive, market]

  playwright:
    tools:
      navigate:
        effectiveness: 0.89
        contexts: [testing, automation, ui-validation]
        complexity_suitability: [simple, medium, complex, expert]
        domains: [testing, ui, automation]

      screenshot:
        effectiveness: 0.91
        contexts: [documentation, validation, debugging]
        complexity_suitability: [simple, medium, complex, expert]
        domains: [testing, documentation, debugging]

      click:
        effectiveness: 0.87
        contexts: [testing, automation, interaction]
        complexity_suitability: [simple, medium, complex, expert]
        domains: [testing, automation]

# Agent-specific tool preferences
agent_tool_preferences:
  james: # Developer Agent
    primary_tools:
      - context7.get-library-docs
      - zen.analyze
      - zen.debug
      - zen.chat
    secondary_tools:
      - zen.codereview
      - zen.refactor
      - perplexity.get_documentation
    complexity_mapping:
      simple: [context7.get-library-docs, zen.chat]
      medium: [context7.get-library-docs, zen.analyze, zen.chat]
      complex: [zen.analyze, zen.debug, zen.thinkdeep, context7.get-library-docs]
      expert: [zen.thinkdeep, zen.consensus, zen.analyze, context7.get-library-docs, perplexity.search]

  code-reviewer: # Code Review Agent
    primary_tools:
      - zen.codereview
      - zen.secaudit
      - zen.analyze
    secondary_tools:
      - zen.testgen
      - zen.precommit
      - playwright.screenshot
    complexity_mapping:
      simple: [zen.codereview]
      medium: [zen.codereview, zen.analyze]
      complex: [zen.codereview, zen.secaudit, zen.analyze]
      expert: [zen.codereview, zen.secaudit, zen.consensus, zen.testgen]

  change-implementer: # Change Implementation Agent
    primary_tools:
      - zen.refactor
      - zen.debug
      - zen.analyze
    secondary_tools:
      - zen.chat
      - context7.get-library-docs
      - zen.codereview
    complexity_mapping:
      simple: [zen.refactor]
      medium: [zen.refactor, zen.debug]
      complex: [zen.refactor, zen.debug, zen.analyze]
      expert: [zen.refactor, zen.debug, zen.thinkdeep, zen.consensus]

  architect: # Architecture Agent
    primary_tools:
      - zen.thinkdeep
      - zen.analyze
      - zen.consensus
    secondary_tools:
      - perplexity.search
      - context7.get-library-docs
      - zen.planner
    complexity_mapping:
      simple: [zen.analyze]
      medium: [zen.analyze, zen.chat]
      complex: [zen.thinkdeep, zen.analyze, zen.consensus]
      expert: [zen.thinkdeep, zen.consensus, zen.planner, perplexity.search]

  analyst: # Research Agent
    primary_tools:
      - perplexity.search
      - firecrawl.search
      - zen.thinkdeep
    secondary_tools:
      - context7.resolve-library-id
      - zen.analyze
      - zen.chat
    complexity_mapping:
      simple: [perplexity.search]
      medium: [perplexity.search, zen.analyze]
      complex: [perplexity.search, firecrawl.search, zen.thinkdeep]
      expert: [zen.thinkdeep, zen.consensus, perplexity.search, firecrawl.search]

  qa-tester: # QA Testing Agent
    primary_tools:
      - zen.testgen
      - playwright.navigate
      - zen.analyze
    secondary_tools:
      - playwright.screenshot
      - zen.codereview
      - zen.secaudit
    complexity_mapping:
      simple: [zen.testgen]
      medium: [zen.testgen, playwright.navigate]
      complex: [zen.testgen, playwright.navigate, zen.analyze]
      expert: [zen.testgen, zen.consensus, playwright.navigate, zen.secaudit]

# Context-based tool selection rules
context_tool_mapping:
  implementation:
    required: [context7.get-library-docs]
    recommended: [zen.analyze, zen.chat]
    optional: [perplexity.get_documentation]

  debugging:
    required: [zen.debug]
    recommended: [zen.analyze, zen.chat]
    optional: [zen.thinkdeep]

  architecture:
    required: [zen.thinkdeep]
    recommended: [zen.consensus, zen.analyze]
    optional: [perplexity.search, zen.planner]

  research:
    required: [perplexity.search]
    recommended: [context7.resolve-library-id, zen.analyze]
    optional: [firecrawl.search, zen.chat]

  testing:
    required: [zen.testgen]
    recommended: [playwright.navigate, zen.analyze]
    optional: [playwright.screenshot, zen.codereview]

  security:
    required: [zen.secaudit]
    recommended: [zen.codereview, zen.analyze]
    optional: [zen.consensus, zen.precommit]

  quality:
    required: [zen.codereview]
    recommended: [zen.analyze, zen.precommit]
    optional: [zen.secaudit, zen.testgen]

# Tool selection algorithms
selection_algorithms:
  context_aware:
    name: Context-Aware Tool Selection
    description: 'Selects tools based on context, complexity, and agent type'
    weight_factors:
      effectiveness: 0.4
      context_match: 0.3
      complexity_suitability: 0.2
      agent_preference: 0.1

  adaptive:
    name: Adaptive Tool Selection
    description: Learns from successful patterns and adapts tool selection
    weight_factors:
      historical_success: 0.5
      effectiveness: 0.3
      context_match: 0.2

  minimal:
    name: Minimal Tool Selection
    description: Selects minimum viable tools for the task
    weight_factors:
      effectiveness: 0.6
      simplicity: 0.4

# Performance optimization settings
optimization:
  cache_duration: 3600 # 1 hour in seconds
  max_concurrent_tools: 3
  timeout_seconds: 300
  retry_attempts: 2

  # Tool combination strategies
  combination_strategies:
    sequential: Execute tools one after another
    parallel: Execute compatible tools simultaneously
    conditional: Execute tools based on previous results

# Monitoring and analytics
monitoring:
  track_effectiveness: true
  log_tool_usage: true
  measure_response_times: true
  collect_user_feedback: true

  metrics:
    - tool_success_rate
    - average_response_time
    - user_satisfaction
    - context_relevance_score
    - complexity_accuracy

# Error handling and fallback strategies
error_handling:
  graceful_degradation: true
  fallback_to_basic_tools: true
  retry_with_timeout: true
  user_notification: true

  fallback_tools:
    implementation: [zen.chat, context7.get-library-docs]
    debugging: [zen.analyze, zen.chat]
    research: [perplexity.search, zen.chat]
    testing: [zen.analyze, zen.chat]
