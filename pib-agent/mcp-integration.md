# MCP Integration Guide for PIB Agents

## Overview
This guide documents how PIB agents leverage MCP (Model Context Protocol) tools to enhance their capabilities with AI-powered analysis, testing, and decision-making.

## Available MCP Tools

### Zen MCP Server (15 Tools)
Currently installed and providing these capabilities:

#### Analysis & Investigation Tools
- **`*mcp-analyze`** - Comprehensive code analysis with expert validation
- **`*mcp-thinkdeep`** - Multi-stage investigation for complex problems
- **`*mcp-debug`** - Systematic root cause analysis
- **`*mcp-tracer`** - Step-by-step code tracing workflow

#### Code Quality Tools
- **`*mcp-codereview`** - Multi-perspective code review
- **`*mcp-refactor`** - Refactoring analysis and recommendations
- **`*mcp-secaudit`** - Security assessment workflow
- **`*mcp-testgen`** - Comprehensive test suite generation

#### Planning & Collaboration Tools
- **`*mcp-planner`** - Interactive sequential planning
- **`*mcp-consensus`** - Multi-model consensus workflow
- **`*mcp-chat`** - AI thinking partner for brainstorming

#### Documentation & Utility Tools
- **`*mcp-docgen`** - Automated documentation generation
- **`*mcp-precommit`** - Pre-commit validation workflow
- **`*mcp-listmodels`** - List available AI models
- **`*mcp-version`** - Check MCP server configuration

### Playwright MCP (Web Testing)
To install: `npx @mcpintegrations/install playwright`

Provides browser automation for:
- **`*mcp-playwright navigate [url]`** - Navigate to web pages
- **`*mcp-playwright click [selector]`** - Click elements
- **`*mcp-playwright fill [selector] [value]`** - Fill form fields
- **`*mcp-playwright screenshot [name]`** - Capture screenshots
- **`*mcp-playwright assert [condition]`** - Validate behavior

## Agent-Specific MCP Usage

### Bill (PM/Orchestrator)
Primary tools for complex orchestration:
```markdown
*bill orchestrate "implement authentication"
  → Uses: *mcp-thinkdeep for comprehensive planning
  → Uses: *mcp-planner for step-by-step workflow
  → Uses: *mcp-consensus for multi-agent decisions
```

### Architect
Architecture and design validation:
```markdown
*architect design "microservices migration"
  → Uses: *mcp-analyze for architecture assessment
  → Uses: *mcp-secaudit for security review
  → Uses: *mcp-refactor for modernization opportunities
```

### Dev
Implementation and debugging:
```markdown
*dev implement "payment processing"
  → Uses: *mcp-debug for issue resolution
  → Uses: *mcp-tracer for code flow understanding
  → Uses: *mcp-refactor for code improvement
```

### QA/Tester
Comprehensive testing:
```markdown
*qa test "checkout flow"
  → Uses: *mcp-testgen for test suite creation
  → Uses: *mcp-playwright for UI testing
  → Uses: *mcp-codereview for test quality
```

### Code Reviewer
Multi-perspective analysis:
```markdown
*reviewer check "PR #123"
  → Uses: *mcp-codereview for comprehensive review
  → Uses: *mcp-secaudit for security checks
  → Uses: *mcp-consensus for approval decisions
```

## Workflow Integration Examples

### Enhanced Module Development
```markdown
# Original workflow
*dev implement → *reviewer check → *changer update

# MCP-enhanced workflow
*dev implement
  → *mcp-analyze before implementation
  → *mcp-debug during development
  → *mcp-testgen for test creation
*reviewer check
  → *mcp-codereview for multi-model analysis
  → *mcp-secaudit for security validation
*changer update
  → *mcp-refactor for improvement opportunities
```

### Complex Problem Solving
```markdown
# Bill encounters complex requirement
*bill: "This needs deep analysis..."
*mcp-thinkdeep "user authentication with SSO, MFA, and social login"
  → Step 1: Analyze requirements and constraints
  → Step 2: Investigate existing patterns
  → Step 3: Design architecture approach
  → Step 4: Identify risks and mitigations
  → Step 5: Create implementation plan
  → Expert validation and recommendations
```

### Web Application Testing
```markdown
# QA testing e-commerce checkout
*qa: "Testing checkout flow with Playwright..."
*mcp-playwright navigate "https://store.example.com"
*mcp-playwright click "a[href='/products']"
*mcp-playwright fill "#search" "laptop"
*mcp-playwright click ".add-to-cart"
*mcp-playwright navigate "/checkout"
*mcp-playwright fill "#email" "<EMAIL>"
*mcp-playwright screenshot "checkout-form"
*mcp-playwright click "button[type='submit']"
*mcp-playwright assert "text=Order Confirmed"
```

## Session Logging

MCP sessions are automatically logged for traceability:

```
.ai/mcp-sessions/
├── 2024-06-24-thinkdeep-auth/
│   ├── session.json
│   ├── step-1-analysis.md
│   ├── step-2-findings.md
│   ├── step-3-design.md
│   ├── step-4-risks.md
│   ├── step-5-plan.md
│   └── expert-recommendations.md
├── 2024-06-24-playwright-checkout/
│   ├── test-results.json
│   ├── screenshots/
│   │   ├── checkout-form.png
│   │   └── order-confirmed.png
│   └── report.html
└── 2024-06-24-codereview-pr123/
    ├── review-summary.md
    ├── issues-found.json
    └── recommendations.md
```

## Best Practices

1. **Use MCP for Complex Tasks**: Simple tasks don't need MCP overhead
2. **Leverage Multi-Model Consensus**: For critical decisions
3. **Document MCP Sessions**: Keep session logs for future reference
4. **Combine Tools**: Use multiple MCP tools in sequence for best results
5. **Agent Specialization**: Each agent should master their relevant MCP tools

## Quick Reference

| Task | Command | Best For |
|------|---------|----------|
| Complex planning | `*mcp-thinkdeep` | Bill (Orchestrator) |
| Code analysis | `*mcp-analyze` | Architect, Dev |
| Debugging | `*mcp-debug` | Dev |
| Code review | `*mcp-codereview` | Reviewer |
| Test generation | `*mcp-testgen` | QA, Dev |
| Web testing | `*mcp-playwright` | QA |
| Security check | `*mcp-secaudit` | Architect, Reviewer |
| Refactoring | `*mcp-refactor` | Dev, Changer |
| Documentation | `*mcp-docgen` | All agents |
| Consensus | `*mcp-consensus` | Bill, Team decisions |

## Getting Help

- Use `*mcp-version` to check MCP configuration
- Use `*mcp-listmodels` to see available AI models
- Use `*mcp-chat` to brainstorm MCP usage strategies
