# LEVER Framework Integration with PIB Method

## When LEVER Principles Are Applied

### 1. **<PERSON>'s Planning Mode** ✅
- Triggered when using <PERSON>'s plan mode
- Automatic reminder when `exit_plan_mode` is called
- Shows LEVER principles before implementation planning

### 2. **PIB Planning Commands** 🔄
The following PIB commands should trigger LEVER thinking:

#### Story & Project Planning
- `*plan-workflow` - When planning command sequences
- `*SM create` - When creating new stories
- `*SM create-with-orchestration` - When creating orchestrated stories
- `*SM doc-shard` - When breaking down large requirements

#### Architecture & Design Planning
- `*Architect Create Architecture` - System architecture planning
- `*Design Architect Create Frontend Architecture` - UI/UX planning
- `*Architect module-design` - Module architecture planning
- `*Platform Engineer design` - Platform-level planning
- `*DevOps infra-plan` - Infrastructure planning

#### Module & Feature Planning
- `*PM Create PRD` - Product requirement planning
- `*PM Create Module PRD` - Module-specific planning
- `*Analyst Create Project Brief` - Initial project planning

## How to Apply LEVER in PIB Context

### During Story Creation (`*SM create`)
Before creating a story, consider:
1. **L** - What existing stories/code can we leverage?
2. **E** - Can we extend an existing feature instead?
3. **V** - How can reactive patterns simplify this?
4. **E** - Are we duplicating any existing functionality?
5. **R** - What's the simplest implementation approach?

### During Architecture Planning (`*Architect`)
Before designing architecture:
1. **L** - What architectural patterns already exist?
2. **E** - Can we extend current architecture?
3. **V** - How can we use framework reactivity?
4. **E** - Are we creating redundant services?
5. **R** - What's the minimal architecture needed?

### During PRD Creation (`*PM Create PRD`)
Before defining requirements:
1. **L** - What similar features already exist?
2. **E** - Can we extend existing functionality?
3. **V** - How can reactive UI reduce complexity?
4. **E** - Are requirements duplicating existing features?
5. **R** - What's the MVP that delivers value?

## LEVER Workflow in PIB

1. **Planning Phase** (Any planning command)
   - LEVER reminder appears
   - Consider reuse opportunities
   - Document extension possibilities

2. **Pre-Implementation** (`*Dev` commands)
   - LEVER checklist runs
   - Complexity assessment
   - Final reuse verification

3. **Code Review** (`*QA` or review process)
   - LEVER compliance check
   - Score 0-5 on LEVER principles
   - Feedback on optimization opportunities

## Quick Reference

### Before ANY PIB Implementation Command:
```
Ask yourself:
1. Can I extend existing code? ✅
2. Can I reuse existing patterns? ✅
3. Can I simplify this approach? ✅
4. Am I creating unnecessary code? ❌
```

### LEVER Score Target:
- Planning: Identify 3+ reuse opportunities
- Implementation: Achieve 4+ LEVER score
- Review: Maintain 5/5 LEVER compliance

Remember: **"The best code is no code. The second best code is code that already exists and works."**
