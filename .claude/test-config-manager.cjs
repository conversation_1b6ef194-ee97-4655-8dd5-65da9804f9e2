#!/usr/bin/env node

/**
 * Test Configuration Manager
 * Manages test configuration, credentials, and environment settings
 */

const crypto = require('node:crypto')
const fs = require('node:fs')
const path = require('node:path')
const readline = require('node:readline')

const CONFIG_FILE = path.join(__dirname, 'test-config.json')
const CREDENTIALS_FILE = path.join(__dirname, '.credentials.enc')

class TestConfigManager {
  constructor() {
    this.config = this.loadConfig()
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    })
  }

  loadConfig() {
    try {
      if (fs.existsSync(CONFIG_FILE)) {
        return JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'))
      }
      return this.getDefaultConfig()
    }
    catch (error) {
      console.error('Error loading config:', error)
      return this.getDefaultConfig()
    }
  }

  getDefaultConfig() {
    return {
      testing: {
        mode: 'headless',
        port: 3000,
        baseUrl: 'http://localhost:3000',
        timeout: {
          default: 30000,
          navigation: 60000,
          assertion: 5000,
        },
        retries: {
          enabled: true,
          count: 2,
          onlyFailures: true,
        },
      },
      browser: {
        defaultBrowser: 'chromium',
        viewport: {
          width: 1280,
          height: 720,
        },
        userAgent: null,
        locale: 'en-US',
        timezone: 'America/New_York',
        permissions: ['geolocation'],
        deviceScaleFactor: 1,
        isMobile: false,
        hasTouch: false,
        colorScheme: 'light',
      },
      credentials: {
        enabled: false,
        storageType: 'encrypted',
        path: CREDENTIALS_FILE,
      },
      environments: {
        local: {
          baseUrl: 'http://localhost:3000',
          port: 3000,
        },
        staging: {
          baseUrl: 'https://staging.example.com',
          port: null,
        },
        production: {
          baseUrl: 'https://example.com',
          port: null,
        },
      },
      firebase: {
        useEmulator: true,
        emulatorPorts: {
          auth: 9099,
          firestore: 8080,
          storage: 9199,
          functions: 5001,
        },
      },
      reporting: {
        screenshots: {
          onFailure: true,
          fullPage: false,
          path: 'test-results/screenshots',
        },
        videos: {
          enabled: false,
          path: 'test-results/videos',
        },
        traces: {
          enabled: true,
          path: 'test-results/traces',
        },
      },
      parallelization: {
        workers: 4,
        fullyParallel: true,
        forbidOnly: true,
      },
    }
  }

  saveConfig() {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(this.config, null, 2))
    console.log(`✅ Configuration saved to ${CONFIG_FILE}`)
  }

  setMode(mode) {
    if (!['headless', 'visible'].includes(mode)) {
      throw new Error('Mode must be either "headless" or "visible"')
    }
    this.config.testing.mode = mode
    this.config.browser.headless = mode === 'headless'
    console.log(`✅ Testing mode set to: ${mode}`)
  }

  setPort(port) {
    const portNum = Number.parseInt(port)
    if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
      throw new Error('Port must be a valid number between 1 and 65535')
    }
    this.config.testing.port = portNum
    this.config.environments.local.port = portNum
    this.config.testing.baseUrl = `http://localhost:${portNum}`
    this.config.environments.local.baseUrl = `http://localhost:${portNum}`
    console.log(`✅ Default port set to: ${portNum}`)
  }

  async saveCredentials() {
    const credentials = {}

    console.log('\n🔐 Credential Setup')
    console.log('Enter credentials for test users (leave blank to skip):\n')

    credentials.testUser = await this.promptCredentials('Test User')
    credentials.adminUser = await this.promptCredentials('Admin User')

    if (Object.keys(credentials).some(key => credentials[key])) {
      const encrypted = this.encryptCredentials(credentials)
      fs.writeFileSync(CREDENTIALS_FILE, encrypted)
      this.config.credentials.enabled = true
      console.log(`\n✅ Credentials saved securely to ${CREDENTIALS_FILE}`)
    }
  }

  async promptCredentials(userType) {
    const email = await this.prompt(`${userType} Email: `)
    if (!email)
      return null

    const password = await this.prompt(`${userType} Password: `, true)
    if (!password)
      return null

    return { email, password }
  }

  prompt(question, hidden = false) {
    return new Promise((resolve) => {
      if (hidden) {
        // For password input, we'll use a simple approach
        console.log(question)
        console.log('(Note: Password will be visible. In production, use a proper password input library)')
      }
      this.rl.question(hidden ? '' : question, (answer) => {
        resolve(answer)
      })
    })
  }

  encryptCredentials(credentials) {
    const algorithm = 'aes-256-cbc'
    const key = crypto.scryptSync('test-config-secret', 'salt', 32)
    const iv = crypto.randomBytes(16)

    const cipher = crypto.createCipheriv(algorithm, key, iv)
    let encrypted = cipher.update(JSON.stringify(credentials), 'utf8', 'hex')
    encrypted += cipher.final('hex')

    return JSON.stringify({
      iv: iv.toString('hex'),
      data: encrypted,
    })
  }

  decryptCredentials() {
    if (!fs.existsSync(CREDENTIALS_FILE)) {
      return null
    }

    try {
      const encrypted = JSON.parse(fs.readFileSync(CREDENTIALS_FILE, 'utf8'))
      const algorithm = 'aes-256-cbc'
      const key = crypto.scryptSync('test-config-secret', 'salt', 32)
      const iv = Buffer.from(encrypted.iv, 'hex')

      const decipher = crypto.createDecipheriv(algorithm, key, iv)
      let decrypted = decipher.update(encrypted.data, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return JSON.parse(decrypted)
    }
    catch (error) {
      console.error('Error decrypting credentials:', error)
      return null
    }
  }

  async interactiveSetup() {
    console.log('🧪 Test Configuration Setup\n')

    // Mode selection
    const mode = await this.prompt('Testing mode (headless/visible) [headless]: ') || 'headless'
    this.setMode(mode)

    // Port selection
    const port = await this.prompt('Default port [3000]: ') || '3000'
    this.setPort(port)

    // Browser selection
    const browser = await this.prompt('Default browser (chromium/firefox/webkit) [chromium]: ') || 'chromium'
    this.config.browser.defaultBrowser = browser

    // Viewport
    const viewportWidth = await this.prompt('Viewport width [1280]: ') || '1280'
    const viewportHeight = await this.prompt('Viewport height [720]: ') || '720'
    this.config.browser.viewport = {
      width: Number.parseInt(viewportWidth),
      height: Number.parseInt(viewportHeight),
    }

    // Save credentials
    const saveCredsAnswer = await this.prompt('\nSave test credentials? (y/n) [n]: ')
    if (saveCredsAnswer.toLowerCase() === 'y') {
      await this.saveCredentials()
    }

    this.saveConfig()
    this.rl.close()
  }

  displayConfig() {
    console.log('\n📋 Current Test Configuration:\n')
    console.log(JSON.stringify(this.config, null, 2))

    if (this.config.credentials.enabled) {
      const creds = this.decryptCredentials()
      if (creds) {
        console.log('\n🔐 Stored Credentials:')
        Object.keys(creds).forEach((key) => {
          if (creds[key]) {
            console.log(`  ${key}: ${creds[key].email}`)
          }
        })
      }
    }
  }

  close() {
    this.rl.close()
  }
}

// CLI argument parsing
async function main() {
  const manager = new TestConfigManager()
  const args = process.argv.slice(2)

  if (args.length === 0) {
    await manager.interactiveSetup()
    return
  }

  try {
    let configChanged = false

    for (let i = 0; i < args.length; i++) {
      const arg = args[i]

      switch (arg) {
        case '--mode':
          manager.setMode(args[++i])
          configChanged = true
          break

        case '--port':
          manager.setPort(args[++i])
          configChanged = true
          break

        case '--save-credentials':
          await manager.saveCredentials()
          manager.close()
          return

        case '--show':
          manager.displayConfig()
          manager.close()
          return

        case '--help':
          console.log(`
Test Configuration Manager

Usage:
  node test-config-manager.js [options]
  
Options:
  --mode <headless|visible>  Set testing mode
  --port <number>           Set default port
  --save-credentials        Save test credentials
  --show                   Display current configuration
  --help                   Show this help message
  
Interactive mode:
  Run without arguments for interactive setup
          `)
          manager.close()
          return

        default:
          console.error(`Unknown option: ${arg}`)
          manager.close()
          process.exit(1)
      }
    }

    if (configChanged) {
      manager.saveConfig()
    }

    manager.close()
  }
  catch (error) {
    console.error(`❌ Error: ${error.message}`)
    manager.close()
    process.exit(1)
  }
}

main().catch(console.error)
