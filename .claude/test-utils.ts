import { test as base, expect } from '@playwright/test'
import { testConfig } from './playwright-config-helper'

/**
 * Extended test fixtures with configuration support
 */

// Get test configuration
const config = testConfig.exportConfig()

// Define custom fixtures
interface TestFixtures {
  authenticatedPage: any
  testCredentials: any
  firebaseConfig: any
}

// Extend base test with custom fixtures
export const test = base.extend<TestFixtures>({
  // Authenticated page fixture
  authenticatedPage: async ({ page, context }, use) => {
    const credentials = config.credentials?.testUser

    if (credentials) {
      // Navigate to login page
      await page.goto('/login')

      // Perform login
      await page.fill('input[name="email"]', credentials.email)
      await page.fill('input[name="password"]', credentials.password)
      await page.click('button[type="submit"]')

      // Wait for authentication
      await page.waitForURL(/dashboard|home/, { timeout: config.timeouts.navigation })

      // Save authentication state
      await context.storageState({ path: '.auth/user.json' })
    }

    await use(page)
  },

  // Test credentials fixture
  testCredentials: async ({}, use) => {
    await use(config.credentials)
  },

  // Firebase config fixture
  firebaseConfig: async ({}, use) => {
    await use(config.firebase)
  },
})

// Re-export expect
export { expect }

/**
 * Utility functions for tests
 */
export class TestHelpers {
  /**
   * Wait for Firebase emulators to be ready
   */
  static async waitForEmulators(page: any) {
    if (!config.firebase?.useEmulator) {
      return
    }

    const ports = config.firebase.emulatorPorts

    // Check Auth emulator
    await page.waitForFunction(
      (authPort) => {
        return fetch(`http://localhost:${authPort}`)
          .then(() => true)
          .catch(() => false)
      },
      ports.auth,
      { timeout: 30000 },
    )

    console.log('✅ Firebase emulators are ready')
  }

  /**
   * Take a screenshot with configuration
   */
  static async screenshot(page: any, name: string) {
    const screenshotConfig = config.reporting.screenshots

    await page.screenshot({
      path: `${screenshotConfig.path}/${name}.png`,
      fullPage: screenshotConfig.fullPage,
    })
  }

  /**
   * Login helper
   */
  static async login(page: any, userType: string = 'testUser') {
    const credentials = config.credentials?.[userType]

    if (!credentials) {
      throw new Error(`No credentials found for ${userType}`)
    }

    await page.goto('/login')
    await page.fill('input[name="email"]', credentials.email)
    await page.fill('input[name="password"]', credentials.password)
    await page.click('button[type="submit"]')

    // Wait for successful login
    await page.waitForURL(/dashboard|home/, {
      timeout: config.timeouts.navigation,
    })
  }

  /**
   * Logout helper
   */
  static async logout(page: any) {
    await page.click('[data-testid="user-menu"]')
    await page.click('[data-testid="logout-button"]')
    await page.waitForURL('/login')
  }

  /**
   * Wait for API response
   */
  static async waitForAPI(page: any, endpoint: string) {
    return page.waitForResponse(
      (response: any) => response.url().includes(endpoint),
      { timeout: config.timeouts.navigation },
    )
  }

  /**
   * Fill form with test data
   */
  static async fillForm(page: any, formData: Record<string, any>) {
    for (const [field, value] of Object.entries(formData)) {
      const input = await page.locator(`[name="${field}"]`)
      const inputType = await input.getAttribute('type')

      switch (inputType) {
        case 'checkbox':
          if (value)
            await input.check()
          else await input.uncheck()
          break
        case 'radio':
          await page.locator(`[name="${field}"][value="${value}"]`).check()
          break
        case 'select':
          await input.selectOption(value)
          break
        default:
          await input.fill(String(value))
      }
    }
  }

  /**
   * Generate test data
   */
  static generateTestData(prefix: string = 'test') {
    const timestamp = Date.now()
    return {
      email: `${prefix}-${timestamp}@example.com`,
      username: `${prefix}_user_${timestamp}`,
      name: `Test User ${timestamp}`,
      phone: `+1234567${String(timestamp).slice(-4)}`,
      timestamp,
    }
  }
}

/**
 * Custom expect matchers
 */
expect.extend({
  async toBeLoggedIn(page) {
    const isLoggedIn = await page.locator('[data-testid="user-menu"]').isVisible()

    if (isLoggedIn) {
      return {
        message: () => 'Expected user not to be logged in',
        pass: true,
      }
    }
    else {
      return {
        message: () => 'Expected user to be logged in',
        pass: false,
      }
    }
  },

  async toHaveNotification(page, text) {
    const notification = await page.locator('.notification', { hasText: text }).isVisible()

    if (notification) {
      return {
        message: () => `Expected not to have notification with text: ${text}`,
        pass: true,
      }
    }
    else {
      return {
        message: () => `Expected to have notification with text: ${text}`,
        pass: false,
      }
    }
  },
})
