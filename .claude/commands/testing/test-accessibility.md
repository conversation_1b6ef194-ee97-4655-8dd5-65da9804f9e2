# Test Accessibility

Comprehensive accessibility compliance testing and analysis.

## Usage
```bash
/project:test-accessibility {target}
```

## Arguments
- `target`: Application URL or service to test for accessibility compliance

## Implementation
Execute comprehensive accessibility testing using automated tools and guidelines:

1. **WCAG Compliance**: Test against WCAG 2.1 AA/AAA standards
2. **Screen Reader Testing**: Validate screen reader compatibility
3. **Keyboard Navigation**: Test full keyboard accessibility
4. **Color Contrast**: Analyze color contrast ratios
5. **Focus Management**: Validate focus indicators and flow

Accessibility standards tested:
- WCAG 2.1 Level A, AA, and AAA
- Section 508 compliance
- ADA (Americans with Disabilities Act) guidelines
- EN 301 549 (European accessibility standard)

Testing categories:
- **Perceivable**: Images, colors, text alternatives
- **Operable**: Keyboard access, navigation, timing
- **Understandable**: Readable content, predictable functionality
- **Robust**: Compatible with assistive technologies

Automated checks include:
- Missing alt text on images
- Insufficient color contrast ratios
- Missing form labels and descriptions
- Improper heading structure (h1-h6)
- Missing ARIA labels and roles
- Keyboard trap detection
- Focus order validation

Manual testing simulation:
- Screen reader navigation paths
- Keyboard-only user workflows
- High contrast mode compatibility
- Browser zoom up to 200%
- Motion sensitivity considerations

Reports generated:
- Detailed violation list with severity levels
- WCAG success criteria compliance matrix
- Remediation recommendations with code examples
- Screen reader testing results
- Color contrast analysis with specific ratios
- Keyboard navigation flow documentation

Arguments: $ARGUMENTS
