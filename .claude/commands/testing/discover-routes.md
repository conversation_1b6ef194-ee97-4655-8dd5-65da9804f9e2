# Discover Routes

Automatically discover and catalog routes from a website or application.

## Usage
```bash
/project:discover-routes {base-url} --depth={1-3} --save-as={workflow-name}
```

## Arguments
- `base-url`: Starting URL for route discovery (e.g., https://app.example.com)
- `--depth`: Crawl depth for route discovery (1-3 levels, default: 2)
- `--save-as`: Optional workflow name to save discovered routes

## Implementation
Automatically crawl and discover all accessible routes in an application:

1. **Site Crawling**: Systematically crawl the website to discover routes
2. **Route Extraction**: Extract unique routes and endpoints
3. **Route Classification**: Categorize routes by type and functionality
4. **Route Validation**: Test route accessibility and response codes
5. **Workflow Generation**: Optionally create a test workflow from discovered routes

Discovery process:
- Start from provided base URL
- Follow internal links up to specified depth
- Extract unique route patterns
- Classify routes by functionality (auth, CRUD, navigation, etc.)
- Test each route for accessibility
- Generate route inventory with metadata

Route classification:
- **Authentication**: Login, logout, registration, password reset
- **Navigation**: Home, about, contact, help pages
- **CRUD Operations**: Create, read, update, delete functionality
- **API Endpoints**: REST API routes and GraphQL endpoints
- **Static Content**: Images, documents, downloads
- **Protected Routes**: Admin panels, user dashboards, private content

Discovery settings:
- **Crawl Depth**: How many link levels to follow
- **Route Filtering**: Include/exclude patterns (e.g., skip /admin)
- **Response Validation**: Check for 200, 404, 403, etc.
- **Rate Limiting**: Respectful crawling with delays
- **User Agent**: Configurable user agent for crawling

Real-time discovery feedback:
```
Discovering routes from: https://staging.example.com
Crawl depth: 2 levels
Rate limit: 1 request/second

Level 1: Crawling base URL...
✓ Found 12 routes from homepage
✓ Classified: 3 navigation, 2 auth, 7 content pages

Level 2: Following internal links...
✓ Found 28 additional routes
✓ Classified: 15 CRUD operations, 8 protected routes, 5 API endpoints

Discovery Summary:
=================
Total Routes Found: 40
- Navigation: 8 routes
- Authentication: 5 routes
- CRUD Operations: 15 routes
- API Endpoints: 7 routes
- Protected Routes: 5 routes

Route Examples:
- /login (authentication)
- /dashboard (protected, requires auth)
- /api/users (API endpoint, CRUD)
- /products/{id} (dynamic route)
- /admin/settings (protected, admin only)
```

Route metadata collected:
- Route pattern and parameters
- HTTP methods supported (GET, POST, PUT, DELETE)
- Authentication requirements
- Response status codes
- Page titles and descriptions
- Form elements and input fields
- Error handling behavior

Auto-generated workflow creation:
```
Creating workflow from discovered routes...

Suggested Workflow: website-full-test
Routes to include: 25/40 (excluding admin and API routes)
Test scenarios generated:
- Authentication flow (login/logout)
- Navigation testing (all public pages)
- Form submissions (contact, search)
- Protected route access validation
- Error page handling (404, 403)

Save this workflow? (y/N): y
✓ Workflow 'website-full-test' created with 25 routes and 45 test scenarios
```

Discovery reports include:
- Complete route inventory with metadata
- Route accessibility matrix
- Suggested test scenarios for each route
- Security considerations (unprotected sensitive routes)
- Performance insights (slow-loading routes)

Arguments: $ARGUMENTS
