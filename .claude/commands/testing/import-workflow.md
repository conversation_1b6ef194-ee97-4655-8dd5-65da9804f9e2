# Import Test Workflow

Import test workflows from external sources or testing frameworks.

## Usage
```bash
/project:import-workflow {file-path} --format={json|yaml|playwright|cypress|postman} --name={workflow-name}
```

## Arguments
- `file-path`: Path to the workflow file to import
- `--format`: Source format (json, yaml, playwright, cypress, postman)
- `--name`: Name for the imported workflow

## Implementation
Import workflows from various testing frameworks and formats:

1. **File Loading**: Load and validate the source workflow file
2. **Format Detection**: Auto-detect format if not specified
3. **Data Extraction**: Extract routes, scenarios, and test data
4. **Conversion**: Convert to internal workflow format
5. **Validation**: Validate imported workflow integrity

Import sources supported:

### JSON/YAML Import
- Generic workflow configurations
- OpenAPI/Swagger specifications
- Custom test framework exports
- CI/CD pipeline configurations

### Playwright Import
- Existing Playwright test files
- Page object model extraction
- Test scenario identification
- Fixture data integration

### Cypress Import
- Cypress test specifications
- Custom command extraction
- Test data and fixtures
- Configuration settings

### Postman Import
- Postman collection files
- Request definitions and test scripts
- Environment variables
- Pre-request and test scripts

### OpenAPI/Swagger Import
- API endpoint discovery
- Request/response schema extraction
- Authentication requirements
- Parameter validation rules

Import process example:
```
Importing workflow from: tests/user-flows.spec.ts
Detected format: Playwright (TypeScript)
Target workflow name: imported-user-flows

Analyzing Playwright tests...
✓ Found 3 test suites
✓ Extracted 8 test scenarios
✓ Identified 5 unique routes
✓ Found page object models
✓ Extracted test data fixtures

Conversion Summary:
==================
Routes discovered:
1. /login (from LoginPage.goto())
2. /dashboard (from navigation assertions)
3. /profile (from ProfilePage references)
4. /settings (from settings test suite)
5. /logout (from logout scenario)

Test scenarios identified:
- Login with valid credentials
- Login with invalid credentials
- Dashboard navigation test
- Profile information update
- Settings configuration change
- Logout functionality
- Session timeout handling
- Error page navigation

Test data extracted:
- User credentials (3 test accounts)
- Profile data templates
- Settings configuration options
- Error message validations
```

Playwright file analysis:
```typescript
// Source: tests/user-flows.spec.ts
test.describe('User Dashboard Flow', () => {
  test('user can access dashboard after login', async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await expect(page).toHaveURL('/dashboard');
  });
});

// Extracted workflow route:
{
  "path": "/login",
  "method": "GET/POST",
  "testData": {
    "email": "<EMAIL>",
    "password": "password123"
  },
  "scenarios": ["valid-login"],
  "assertions": ["redirect-to-dashboard"]
}
```

OpenAPI import example:
```yaml
# Source: api-spec.yaml
paths:
  /api/users:
    get:
      summary: List users
      responses:
        200:
          description: Successful response
    post:
      summary: Create user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'

# Converted workflow route:
{
  "path": "/api/users",
  "method": "GET",
  "scenarios": ["list-users-success", "unauthorized-access"],
  "assertions": ["200-response", "user-array-structure"]
},
{
  "path": "/api/users",
  "method": "POST",
  "testData": {
    "name": "Test User",
    "email": "<EMAIL>"
  },
  "scenarios": ["create-user-success", "validation-errors"],
  "assertions": ["201-response", "user-created"]
}
```

Import validation includes:
- Route accessibility verification
- Test data format validation
- Scenario logic consistency check
- Authentication requirement analysis
- Dependencies and execution order validation

Conflict resolution:
- Handle duplicate route definitions
- Merge overlapping test scenarios
- Resolve naming conflicts
- Preserve existing workflow data

After successful import:
```
✓ Workflow 'imported-user-flows' created successfully

Import Summary:
==============
Source: tests/user-flows.spec.ts (Playwright)
Routes imported: 5
Scenarios imported: 8
Test data sets: 3
Estimated execution time: 6-8 minutes

Workflow ready for execution. Use:
/project:run-workflow imported-user-flows

Next steps:
- Review imported routes and scenarios
- Validate test data and credentials
- Execute workflow to verify functionality
- Customize scenarios as needed
```

Arguments: $ARGUMENTS
