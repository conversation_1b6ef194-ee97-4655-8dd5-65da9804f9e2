# Delete Test Workflow

Remove a test workflow from saved configurations.

## Usage
```bash
/project:delete-workflow {workflow-name}
```

## Arguments
- `workflow-name`: Name of the workflow to delete

## Implementation
Safely remove test workflows with confirmation and backup:

1. **Workflow Lookup**: Find and display workflow details
2. **Impact Assessment**: Check if workflow is referenced by other systems
3. **Confirmation Prompt**: Request user confirmation with workflow summary
4. **Backup Creation**: Create backup before deletion
5. **Clean Removal**: Remove workflow and associated files

Safety features:
- Interactive confirmation with workflow summary
- Automatic backup creation before deletion
- Impact analysis for dependent workflows
- Graceful handling of missing workflows
- Recovery options through backup restoration

Deletion process:
1. Locate workflow configuration file
2. Display workflow summary (routes, scenarios, last used)
3. Check for dependencies or references
4. Prompt for confirmation with detailed information
5. Create timestamped backup
6. Remove workflow configuration and associated data
7. Clean up any cached test data or reports

Confirmation prompt:
```
Found workflow to delete: user-registration-flow

Workflow Details:
================
Description: Complete user registration and onboarding
Routes: 4 routes (/signup, /verify-email, /welcome, /profile)
Scenarios: 12 test scenarios
Created: 2024-01-15
Last Used: 2024-01-20
Success Rate: 100% (last 10 runs)
Dependencies: None

Associated Data:
- Test execution reports (5 recent reports)
- Cached screenshots and test artifacts
- Performance baseline data

⚠️  This action cannot be undone without restoring from backup.

Are you sure you want to delete workflow 'user-registration-flow'? (y/N):
```

Impact assessment includes:
- Check for workflows that reference this workflow
- Identify scheduled test runs using this workflow
- List any reports or analytics depending on this workflow
- Warn about loss of historical test data

Backup creation:
- Complete workflow configuration saved
- Associated test data and reports archived
- Timestamped backup file created
- Backup location logged for recovery reference

After successful deletion:
```
✓ Workflow 'user-registration-flow' deleted successfully

Cleanup Summary:
===============
- Workflow configuration removed
- Associated test data cleaned up
- 5 test reports archived
- Cached artifacts removed

Backup Information:
==================
Backup File: .claude/workflows/backups/user-registration-flow.2024-01-20-143022.backup
Contains: Complete workflow configuration and associated data
Recovery: Use /project:restore-workflow to recover if needed

The workflow has been permanently removed from active configurations.
```

Recovery options:
- Backup files preserved for 90 days
- Restore command available for backup recovery
- Manual configuration reconstruction from backup data

Arguments: $ARGUMENTS
