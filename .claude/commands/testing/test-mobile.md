# Test Mobile

Comprehensive mobile responsiveness and functionality testing.

## Usage
```bash
/project:test-mobile {target}
```

## Arguments
- `target`: Application URL or service to test for mobile compatibility

## Implementation
Execute comprehensive mobile testing across devices and scenarios:

1. **Device Emulation**: Test across popular mobile devices and screen sizes
2. **Touch Interaction**: Validate touch gestures and mobile-specific interactions
3. **Responsive Design**: Test layout adaptation across breakpoints
4. **Performance**: Mobile-specific performance metrics and optimization
5. **Native Features**: Test mobile browser features and capabilities

Device coverage:
- iPhone (various models and iOS versions)
- Android phones (Samsung, Google Pixel, etc.)
- iPad and Android tablets
- Foldable devices and unusual aspect ratios
- Custom viewport dimensions

Mobile-specific testing:
- Touch target size validation (minimum 44px)
- Swipe gestures and scroll behavior
- Pinch-to-zoom functionality
- Orientation change handling (portrait/landscape)
- Mobile keyboard interactions
- Pull-to-refresh functionality

Responsive design validation:
- Breakpoint behavior (320px, 768px, 1024px, etc.)
- Layout integrity across screen sizes
- Image scaling and optimization
- Typography scaling and readability
- Navigation menu adaptation (hamburger menus)
- Button and form element sizing

Mobile performance testing:
- Network throttling (3G, 4G simulation)
- Battery usage simulation
- Memory constraints
- CPU throttling
- Touch responsiveness latency

Mobile browser features:
- Geolocation API functionality
- Camera and photo upload
- Device orientation detection
- Push notification compatibility
- Offline functionality testing
- Service worker behavior

Reports include mobile-specific metrics and recommendations for mobile optimization.

Arguments: $ARGUMENTS
