# Enable LEVER Gates

Enable mandatory LEVER architecture validation at all workflow phases.

## Usage
```bash
/project:enable-lever-gates --all-phases --strict --score-target=4
```

## Arguments
- `--all-phases`: Enable LEVER gates in all workflow phases (default: true)
- `--strict`: Require LEVER compliance to proceed (default: true)
- `--score-target`: Minimum LEVER score required (3-5, default: 4)
- `--disable`: Disable LEVER gates (not recommended)

## Implementation
Configure comprehensive LEVER architecture validation throughout all PIB workflows:

### LEVER Gate Integration Points:

#### 1. **Planning Phase Gates**
- **Command**: `/dev`, `/lever-workflow`, planning commands
- **Trigger**: Before any implementation planning
- **Validation**:
  - Search existing codebase for similar functionality
  - Identify extension opportunities
  - Assess complexity reduction potential
  - Generate initial LEVER score projection

#### 2. **Design Phase Gates**
- **Command**: `/architect-review`, design commands
- **Trigger**: During architecture and design planning
- **Validation**:
  - Validate extension-first design approach
  - Verify reactive pattern integration
  - Assess architectural complexity
  - Ensure duplication elimination

#### 3. **Implementation Phase Gates**
- **Command**: All development commands
- **Trigger**: During code implementation
- **Validation**:
  - Real-time LEVER score monitoring
  - Extension compliance checking
  - Reactive pattern verification
  - Complexity assessment

#### 4. **Review Phase Gates**
- **Command**: `/codereview`, quality assurance commands
- **Trigger**: During code review processes
- **Validation**:
  - Comprehensive LEVER audit
  - Final score calculation
  - Compliance certification
  - Improvement recommendations

#### 5. **Testing Phase Gates**
- **Command**: Testing and workflow commands
- **Trigger**: During test execution
- **Validation**:
  - Test LEVER compliance
  - Validate extension testing
  - Verify reactive test patterns

### LEVER Gate Configuration:

#### Strict Mode (Default)
```bash
# Strict enforcement - workflow stops if LEVER target not met
LEVER_STRICT_MODE=true
LEVER_TARGET_SCORE=4
LEVER_FAIL_ON_MISS=true
LEVER_REQUIRE_DOCUMENTATION=true
```

#### Lenient Mode
```bash
# Warning mode - workflow continues with warnings
LEVER_STRICT_MODE=false
LEVER_TARGET_SCORE=3
LEVER_FAIL_ON_MISS=false
LEVER_REQUIRE_DOCUMENTATION=false
```

### Gate Activation Process:

1. **Hook Integration**: LEVER gates integrated into all existing PIB hooks
2. **Command Enhancement**: All commands updated with LEVER validation
3. **Workflow State**: LEVER scores tracked in workflow state
4. **Quality Gates**: LEVER compliance required for progression
5. **Documentation**: LEVER compliance automatically documented

### Real-Time LEVER Monitoring:

```
LEVER Architecture Gates Status
==============================
Project: user-authentication-feature
Target Score: 4/5 (Strict Mode: ON)

Gate Status:
============
✓ Planning Gate    - PASSED (Score: 4.2/5)
✓ Design Gate      - PASSED (Score: 4.0/5)
⏳ Implementation   - IN PROGRESS (Current: 3.8/5)
⏸ Review Gate      - PENDING
⏸ Testing Gate     - PENDING

Current Phase: Implementation
LEVER Score: 3.8/5 (Target: 4.0/5)
Status: ⚠ Below Target - Optimization Needed

LEVER Breakdown:
===============
L - Leverage:    4/5  ✓ Good reuse of existing auth patterns
E - Extend:      4/5  ✓ Extended BaseAuth component successfully
V - Verify:      3/5  ⚠ More reactive validation needed
E - Eliminate:   5/5  ✓ No duplication detected
R - Reduce:      3/5  ⚠ Complexity can be reduced

Recommendations:
===============
1. Add reactive validation patterns (+0.5 Verify score)
2. Simplify authentication flow (+0.5 Reduce score)
3. Leverage more framework reactivity

Estimated Target Achievement: +0.3 points (Final: 4.1/5)
```

### Gate Enforcement Actions:

#### On LEVER Score Below Target:
1. **Warning Display**: Clear warning about LEVER compliance
2. **Recommendations**: Specific suggestions for improvement
3. **Blocking**: Workflow stopped until compliance achieved (strict mode)
4. **Documentation**: Non-compliance reasons documented

#### On LEVER Gate Failure:
1. **Automatic Review**: Trigger additional LEVER-focused review
2. **Refactoring Required**: Mandatory code improvement phase
3. **Re-validation**: Re-run LEVER assessment after changes
4. **Escalation**: Senior review if repeated failures

### LEVER Gate Reporting:

#### Daily LEVER Dashboard:
```
LEVER Architecture Compliance Dashboard
======================================
Date: 2024-01-20

Overall Project Health:
======================
✓ Active Workflows: 3
✓ LEVER Compliant: 2 (67%)
⚠ Below Target: 1 (33%)
✗ Failed Gates: 0 (0%)

Average LEVER Score: 4.1/5
Target Achievement Rate: 78%
Improvement Trend: ↗ +0.3 vs last week

Gate Performance:
================
Planning Gates:    95% pass rate
Design Gates:      88% pass rate
Implementation:    72% pass rate
Review Gates:      96% pass rate
Testing Gates:     85% pass rate

Top LEVER Principles:
====================
1. Eliminate (4.6/5) - Excellent duplication prevention
2. Extend (4.3/5) - Strong extension-first approach
3. Leverage (4.1/5) - Good pattern reuse
4. Reduce (3.8/5) - Room for complexity improvement
5. Verify (3.7/5) - Need more reactive patterns
```

### Integration with Existing Commands:

All existing PIB commands are enhanced with LEVER gates:
- `/dev` → LEVER pre-check + continuous monitoring
- `/codereview` → LEVER compliance audit
- `/architect-review` → LEVER architecture validation
- `/test-*` → LEVER testing pattern verification
- All workflow commands → Phase-specific LEVER validation

### Configuration Files:

#### `.claude/lever-gates.json`
```json
{
  "enabled": true,
  "strictMode": true,
  "targetScore": 4,
  "phases": {
    "planning": true,
    "design": true,
    "implementation": true,
    "review": true,
    "testing": true
  },
  "enforcement": {
    "blockOnFailure": true,
    "requireDocumentation": true,
    "autoReview": true
  }
}
```

The system ensures LEVER architecture principles are consistently applied across all development phases.

Arguments: $ARGUMENTS
