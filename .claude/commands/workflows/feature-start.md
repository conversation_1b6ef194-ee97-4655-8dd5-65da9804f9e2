# Start New Feature Development

Creates a new Git worktree with isolated state for parallel feature development.

## Usage
```
/feature-start <feature-name> [base-branch]
```

## Parameters
- `feature-name`: Name for your feature (letters, numbers, hyphens only)
- `base-branch`: Optional base branch (defaults to 'main')

## What This Command Does

### 1. Worktree Creation
- Creates a new Git worktree in `../pib-<feature-name>/`
- Creates branch `feature/<feature-name>-YYYYMMDD`
- Isolated from main repository and other features

### 2. State Isolation
- Creates dedicated state directory: `.claude/state/worktrees/<feature-name>/`
- Copies essential configuration from main state
- Initializes isolated Context Engineering state
- Prevents cross-contamination between parallel features

### 3. Environment Files
- Automatically searches for all `.env*` files in the project
- Copies them to the worktree preserving directory structure
- Includes `.env`, `.env.local`, `.env.production`, etc.
- Handles nested `.env` files (e.g., `frontend/.env`, `backend/.env`)

### 4. PIB Integration
- Full PIB framework available in new worktree
- Isolated tool selection and context evolution
- Separate MCP conversation tracking
- Independent workflow state management

## Examples

### Basic Feature
```
/feature-start user-authentication
```
Creates: `../pib-user-authentication/` with branch `feature/user-authentication-20250704`

### Feature from Specific Branch
```
/feature-start payment-gateway develop
```
Creates worktree based on 'develop' branch instead of 'main'

### Complex Feature Names
```
/feature-start user-profile-redesign
/feature-start api-v2-migration
/feature-start mobile-responsive-ui
```

## Next Steps After Creation

1. **Open New Claude Code Instance**
   ```bash
   cd ../pib-<feature-name>
   claude-code
   ```

2. **Develop Using Normal PIB Workflows**
   - Use `/dev`, `/pm-orchestrate`, `/architect-review` as usual
   - All state and context will be isolated to this feature
   - No interference with other parallel development

3. **When Ready to Integrate**
   ```
   /feature-merge
   ```

## Safety Features

### Validation
- Prevents duplicate worktree names
- Validates feature name format
- Ensures you're in main repository
- Checks base branch exists

### State Protection
- Automatic backup of main state before operations
- Template-based state initialization
- Read-only shared configuration
- Rollback capability on failures

### Error Handling
- Automatic cleanup on failed creation
- Clear error messages with solutions
- Graceful handling of Git conflicts
- State validation before proceeding

## Troubleshooting

### "Worktree directory already exists"
- Choose a different feature name
- Or remove existing worktree: `/feature-cleanup <name>`

### "Could not checkout base branch"
- Ensure base branch exists and is accessible
- Try `git fetch` to update remote branches

### "Must be in main repository"
- Run this command from the main project directory
- Not from within an existing worktree

## Advanced Usage

### Working with Multiple Features
```bash
# Terminal 1
/feature-start user-auth
cd ../pib-user-auth
# Develop authentication...

# Terminal 2
/feature-start payment-system
cd ../pib-payment-system
# Develop payments...
```

Each worktree has completely isolated:
- Git branches and history
- PIB state and context
- Tool selection and caching
- MCP conversation history
- Workflow evolution chains

### Integration with Existing Workflows
- All existing PIB commands work normally
- Context Engineering provides feature-specific context
- Agent orchestration operates independently
- Quality gates and reviews are isolated

## Related Commands
- `/feature-list` - Show active worktrees
- `/feature-merge` - Integrate changes back
- `/feature-cleanup` - Remove completed worktrees

---
**Note**: This command requires Git worktree support and bash environment. Works seamlessly with the existing PIB Context Engineering framework.
