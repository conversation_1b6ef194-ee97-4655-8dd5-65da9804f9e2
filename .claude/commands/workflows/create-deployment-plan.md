---
description: Create comprehensive deployment plan using DevOps expertise for reliable releases
---

# PIB Create Deployment Plan Command

**Usage**: `*create-deployment-plan [environment] [options]`

## Purpose

Creates comprehensive deployment plans using DevOps expertise, leveraging PIB principles to build upon existing infrastructure patterns while extending capabilities for reliable, repeatable deployments.

### Key Benefits
- Leverages existing infrastructure and deployment patterns in the project
- Extends current architecture with detailed deployment specifications
- Reduces deployment risk through comprehensive planning and automation
- Integrates seamlessly with PIB development and testing workflows

### When to Use
- After system architecture and implementation are substantially complete
- When preparing for production deployment or new environment setup
- As part of DevOps workflow planning and infrastructure management
- When standardizing deployment processes across environments

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing infrastructure, CI/CD pipelines, and deployment tools
- **E** - Extend: Current deployment capabilities with enhanced automation
- **V** - Verify: Deployment strategies against reliability and rollback requirements
- **E** - Eliminate: Manual deployment steps and configuration drift
- **R** - Reduce: Deployment complexity while maximizing reliability and speed

#### 2. Context Gathering
```bash
# Load project and infrastructure context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "devops"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
ARCHITECTURE=$(cat docs/architecture.md 2>/dev/null || echo "No architecture found")
INFRASTRUCTURE=$(cat docs/platform-architecture.md 2>/dev/null || echo "No platform architecture found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If DevOps Active
- Focus on comprehensive deployment automation and infrastructure as code
- Detailed CI/CD pipeline specifications and environment management
- Monitoring, logging, and incident response planning

##### If Platform Engineer Active
- Emphasize platform-level deployment orchestration and service mesh
- Container orchestration and microservices deployment strategies
- Developer self-service and platform automation capabilities

#### 4. Core Processing
- Load DevOps persona and deployment planning task
- Analyze existing architecture and infrastructure requirements
- Create comprehensive deployment plan using established template
- Document deployment pipelines, environment configurations, and rollback procedures
- Generate infrastructure as code and automation specifications

#### 5. Output Generation
- Structured deployment plan saved to `docs/deployment-plan.md`
- Infrastructure as code specifications and configuration templates
- CI/CD pipeline definitions and deployment automation scripts
- Environment setup and configuration management documentation

#### 6. Workflow Integration
- Updates workflow state to indicate deployment readiness
- Prepares context for production deployment and monitoring setup
- Integrates with existing development and testing workflows

## Quality Features / Validation Checklist

### PIB Compliance Validation
- [ ] **LEVER Principles**: Leverages existing infrastructure patterns and tools
- [ ] **Agent Alignment**: Uses DevOps or Platform Engineer expertise
- [ ] **Workflow Integration**: Properly follows deployment workflow sequence
- [ ] **Knowledge Capture**: Documents deployment decisions and automation strategies
- [ ] **Context Awareness**: Builds upon existing architecture and infrastructure context

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing architecture or incomplete infrastructure specs
- [ ] **Error Handling**: Provides guidance when deployment prerequisites are missing
- [ ] **Performance**: Efficient deployment planning process
- [ ] **Consistency**: Maintains consistent deployment patterns across environments
- [ ] **Documentation**: Clear, implementable deployment specifications

### Output Quality Assurance
- [ ] **Completeness**: Covers all deployment environments and scenarios
- [ ] **Accuracy**: Deployment specifications are technically sound
- [ ] **Relevance**: Plan aligns with project requirements and operational needs
- [ ] **Actionability**: Provides specific implementation guidance for deployment
- [ ] **Integration**: Seamlessly connects with development and testing workflows

### Knowledge Management Standards
- [ ] **Persistence**: Saves deployment plan to `docs/deployment-plan.md`
- [ ] **Versioning**: Maintains deployment strategy history and iterations
- [ ] **Cross-Reference**: Links to architecture and infrastructure documentation
- [ ] **Searchability**: Structured for easy operational reference
- [ ] **Agent Context**: Updates DevOps and development team knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for deployment readiness state
- **Notifications**: `notification-hook.sh` for deployment plan completion alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Deployment plan to `docs/deployment-plan.md`
- **Updates**: Project context with deployment specifications in `.ai/project-context.md`
- **Creates**: Deployment automation tracking in `.ai/deployment/` directory
- **Links**: Cross-references with architecture and testing documentation

### Agent Context Updates
- **DevOps**: Updates deployment knowledge and automation templates
- **All Agents**: Updates shared project deployment context
- **Development**: Provides deployment requirements and integration points
- **QA**: Provides deployment testing criteria and validation procedures

### Follow-up Command Preparation
Results prepare context for:
- `*validate-infrastructure` - Validate deployment plan against infrastructure
- `*create-test-plan` - Testing strategies for deployment validation
- `*update-knowledge` - Distribute deployment specifications to all agents
- `*devops-deploy` - Execute deployment using created plan

## Related Commands

### Core Infrastructure Commands
- `*create-infrastructure-architecture` - Prerequisites for deployment planning
- `*validate-infrastructure` - Validate deployment plan feasibility
- `*platform-change-management` - Platform-level deployment coordination

### Workflow Commands
- `*create-test-plan` - Testing strategies for deployment validation
- `*review-infrastructure` - Infrastructure readiness assessment
- `*devops-deploy` - Execute deployment using plan

### Agent Commands
- `/switch-agent devops` - Optimal agent for deployment planning
- `/switch-agent platform-engineer` - Platform-focused deployment strategies

### Knowledge Commands
- `/update-knowledge` - Distribute deployment specifications to all agents
- `/memory-extract` - Extract deployment insights and operational knowledge

## Example Usage

### Basic Usage
```bash
# Create deployment plan for current project
*create-deployment-plan production
```

### Advanced Usage
```bash
# Create plan with specific deployment strategy
*create-deployment-plan staging --strategy=blue-green --containers
```

### Agent-Specific Usage
```bash
# When DevOps agent is active:
*create-deployment-plan --comprehensive --automation

# When Platform Engineer agent is active:
*create-deployment-plan --microservices --orchestration
```

### Workflow Integration Example
```bash
# Part of pre-deployment workflow:
/architect-design
/create-infrastructure-architecture
*create-deployment-plan production  # ← Comprehensive deployment planning
/validate-infrastructure
/create-test-plan
/update-knowledge
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **DevOps**: Comprehensive deployment automation with CI/CD focus
- **Platform Engineer**: Platform-level orchestration and service mesh deployment
- **Developer**: Implementation-focused deployment requirements
- **QA**: Testing integration and deployment validation procedures
- **Architect**: Infrastructure alignment and deployment architecture

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing infrastructure patterns, CI/CD tools, and deployment frameworks
- **Extend**: Current deployment capabilities with enhanced automation and reliability
- **Verify**: Deployment strategies against operational requirements and SLAs
- **Eliminate**: Manual deployment steps and environment configuration drift
- **Reduce**: Deployment complexity while maximizing reliability and recovery capabilities

### Best Practices
- Run after architecture and infrastructure planning for optimal context
- Include rollback strategies and disaster recovery procedures
- Integrate with existing CI/CD pipelines and automation tools
- Document operational runbooks and incident response procedures
