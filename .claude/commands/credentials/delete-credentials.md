# Delete Credentials

Remove stored credentials from secure storage.

## Usage
```bash
/project:delete-credentials {site-name}
```

## Arguments
- `site-name`: Name identifier of the credentials to delete

## Implementation
Securely remove stored credentials with confirmation:

1. **Credential Lookup**: Find credentials by site name identifier
2. **Confirmation Prompt**: Display credential details and request confirmation
3. **Secure Deletion**: Remove credentials from encrypted storage
4. **Cleanup**: Clear any related cached authentication data
5. **Verification**: Confirm successful deletion

Safety features:
- Interactive confirmation before deletion
- Display of credential metadata (without sensitive data)
- Option to cancel deletion process
- Secure overwrite of credential data
- Audit log of deletion events

Deletion process:
1. Search for credentials matching the provided site name
2. Display credential summary (username, creation date, last used)
3. Prompt for confirmation: "Delete credentials for '{site-name}'? (y/N)"
4. If confirmed, securely remove from system credential manager
5. Clear any cached session data or tokens
6. Log deletion event with timestamp

Confirmation prompt example:
```
Found credentials for: staging-app
User: test***@company.com
Created: 2024-01-15
Last Used: 2024-01-20

Are you sure you want to delete these credentials? (y/N):
```

Security considerations:
- Credentials are securely overwritten, not just marked for deletion
- Related authentication tokens are also cleared
- System credential manager entries are completely removed
- No recovery option once deletion is confirmed

The command ensures safe credential lifecycle management for testing environments.

Arguments: $ARGUMENTS
