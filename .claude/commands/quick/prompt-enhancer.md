# Quick: Prompt Enhancer

**Quickly enhance prompts with structured improvements and best practices.**

## Command
```bash
/prompt-enhancer
```

Enhanced with specific prompt:
```bash
/prompt-enhancer "Your prompt here"
```

## Purpose
Transform basic prompts into comprehensive, structured requests that produce better AI responses through:
- Context enrichment
- Specification of desired output format
- Addition of quality constraints
- Inclusion of edge cases
- Clear success criteria

## Implementation

### 1. **Prompt Analysis**
- Parse the original prompt for intent
- Identify missing context or constraints
- Detect vague language or ambiguous requests
- Assess completeness and specificity

### 2. **Enhancement Categories**
- **Context Addition**: Background information, constraints, environment
- **Output Specification**: Format, structure, length, style requirements
- **Quality Criteria**: Success metrics, edge cases, validation rules
- **Examples**: Provide concrete examples when helpful
- **Constraints**: Technical, business, or practical limitations

### 3. **Structured Output**
Enhanced prompts follow this template:
```
## Context
[Background and environment details]

## Objective
[Clear, specific goal statement]

## Requirements
- [Functional requirements]
- [Quality constraints]
- [Technical specifications]

## Output Format
[Desired structure and presentation]

## Success Criteria
[How to measure success]

## Examples
[Concrete examples if helpful]
```

## Common Enhancement Patterns

### Code Request Enhancement
**Before**: "Write a function to sort data"
**After**:
```
## Context
Working on a performance-critical data processing system in Python 3.9+

## Objective
Create an efficient sorting function for large datasets (10K+ records)

## Requirements
- Handle mixed data types (strings, numbers, dates)
- Preserve original order for equal elements (stable sort)
- Memory efficient for large datasets
- Include error handling for invalid data

## Output Format
- Complete function with docstring
- Type hints
- Example usage
- Unit tests

## Success Criteria
- O(n log n) time complexity
- Handles edge cases (empty lists, single elements)
- Comprehensive error messages
```

### Analysis Request Enhancement
**Before**: "Analyze this code"
**After**:
```
## Context
Legacy codebase migration project, need to assess technical debt

## Objective
Comprehensive code analysis for refactoring planning

## Requirements
- Code quality assessment (maintainability, readability)
- Security vulnerability identification
- Performance bottleneck analysis
- Dependencies and coupling evaluation

## Output Format
- Executive summary
- Detailed findings by category
- Prioritized improvement recommendations
- Effort estimates for fixes

## Success Criteria
- Actionable recommendations
- Risk level classification
- Clear migration path
```

## Quality Features

### **Context Enrichment**
- Project type and constraints
- Technology stack and versions
- Performance requirements
- Team skill level considerations

### **Specification Clarity**
- Concrete examples over abstract descriptions
- Measurable success criteria
- Clear deliverable formats
- Edge case handling

### **Best Practice Integration**
- Industry standards references
- Security considerations
- Performance guidelines
- Maintainability principles

## Usage Examples

### Basic Enhancement
```bash
/prompt-enhancer "Help me debug this code"
```

### Complex Request
```bash
/prompt-enhancer "Design a database schema for an e-commerce platform"
```

### Analysis Task
```bash
/prompt-enhancer "Review my architecture design"
```

## Integration with PIB Method

- **LEVER Principles**: Prompts encourage reuse and extension
- **Quality Focus**: Built-in quality criteria and validation
- **Agent Context**: Tailored enhancements based on current agent persona
- **Workflow Integration**: Enhanced prompts align with PIB workflows

## Related Commands
- `/help` - Show command reference
- `/doc-out` - Output full documents for context
- `/switch-agent` - Change agent context for specialized enhancement
