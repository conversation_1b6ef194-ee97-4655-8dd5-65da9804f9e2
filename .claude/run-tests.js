#!/usr/bin/env node
/**
 * PIB Calendar Test Runner
 *
 * Unified test runner that uses the test configuration
 * to run tests across all modules and apps
 */

import { spawn } from 'node:child_process'
import fs from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const PROJECT_ROOT = path.resolve(__dirname, '..')
const CONFIG_FILE = path.join(__dirname, 'test-config.json')

class TestRunner {
  constructor() {
    this.config = null
    this.verbose = false
  }

  async loadConfig() {
    try {
      const configData = await fs.readFile(CONFIG_FILE, 'utf8')
      this.config = JSON.parse(configData)
    }
    catch (error) {
      console.error('Failed to load test configuration:', error.message)
      process.exit(1)
    }
  }

  async runCommand(command, cwd = PROJECT_ROOT) {
    return new Promise((resolve, reject) => {
      if (this.verbose) {
        console.log(`🏃 Running: ${command} in ${cwd}`)
      }

      const [cmd, ...args] = command.split(' ')
      const process = spawn(cmd, args, {
        cwd,
        stdio: this.verbose ? 'inherit' : 'pipe',
        shell: true,
      })

      let stdout = ''
      let stderr = ''

      if (!this.verbose) {
        process.stdout?.on('data', (data) => {
          stdout += data.toString()
        })

        process.stderr?.on('data', (data) => {
          stderr += data.toString()
        })
      }

      process.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, stdout, stderr })
        }
        else {
          reject({
            success: false,
            code,
            stdout,
            stderr,
            command,
          })
        }
      })

      process.on('error', (error) => {
        reject({
          success: false,
          error: error.message,
          command,
        })
      })
    })
  }

  async startFirebaseEmulators() {
    if (!this.config.firebase.emulators.enabled) {
      console.log('📢 Firebase emulators disabled, skipping...')
      return
    }

    console.log('🔥 Starting Firebase emulators...')
    try {
      await this.runCommand('pnpm emulators &')
      // Wait for emulators to start
      await new Promise(resolve => setTimeout(resolve, 5000))
      console.log('✅ Firebase emulators started')
    }
    catch (error) {
      console.warn('⚠️ Failed to start Firebase emulators:', error.stderr || error.error)
    }
  }

  async runModuleTests(module, testType = 'all') {
    const moduleConfig = this.config.modules[module]
    if (!moduleConfig) {
      console.error(`❌ Module '${module}' not found in configuration`)
      return false
    }

    console.log(`\n🧪 Testing ${module} module (${testType})`)
    console.log('='.repeat(50))

    const moduleDir = path.join(PROJECT_ROOT, moduleConfig.testDir, '..')

    try {
      switch (testType) {
        case 'unit':
          await this.runCommand('pnpm test:unit', moduleDir)
          break
        case 'integration':
          await this.runCommand('pnpm test:integration', moduleDir)
          break
        case 'e2e':
          await this.runCommand('pnpm test:e2e', moduleDir)
          break
        case 'coverage':
          await this.runCommand('pnpm test:coverage', moduleDir)
          break
        case 'all':
          await this.runCommand('pnpm test', moduleDir)
          break
        default:
          console.error(`❌ Unknown test type: ${testType}`)
          return false
      }

      console.log(`✅ ${module} ${testType} tests passed`)
      return true
    }
    catch (error) {
      console.error(`❌ ${module} ${testType} tests failed:`)
      if (this.verbose) {
        console.error(error.stderr || error.error)
      }
      return false
    }
  }

  async runAllTests(testType = 'all') {
    await this.loadConfig()

    console.log(`\n🚀 Running ${testType} tests for all modules`)
    console.log('='.repeat(60))

    // Start emulators if needed
    await this.startFirebaseEmulators()

    const results = {}
    const modules = Object.keys(this.config.modules)

    for (const module of modules) {
      results[module] = await this.runModuleTests(module, testType)
    }

    // Summary
    console.log('\n📊 Test Results Summary')
    console.log('='.repeat(30))

    let passed = 0
    let failed = 0

    for (const [module, success] of Object.entries(results)) {
      const status = success ? '✅ PASSED' : '❌ FAILED'
      console.log(`${module}: ${status}`)
      if (success)
        passed++
      else failed++
    }

    console.log(`\nTotal: ${passed} passed, ${failed} failed`)

    if (failed > 0) {
      process.exit(1)
    }

    return results
  }

  async runSpecificTest(pattern) {
    await this.loadConfig()

    console.log(`\n🎯 Running tests matching: ${pattern}`)

    // Try to find the test in each module
    for (const [module, moduleConfig] of Object.entries(this.config.modules)) {
      const moduleDir = path.join(PROJECT_ROOT, moduleConfig.testDir, '..')
      try {
        await this.runCommand(`pnpm test ${pattern}`, moduleDir)
        console.log(`✅ Found and ran test in ${module}`)
        return true
      }
      catch (error) {
        // Continue searching in other modules
      }
    }

    console.error(`❌ No tests found matching pattern: ${pattern}`)
    return false
  }

  async validateSetup() {
    await this.loadConfig()

    console.log('🔍 Validating test setup...')

    const issues = []

    // Check if pnpm is available
    try {
      await this.runCommand('pnpm --version')
    }
    catch {
      issues.push('pnpm is not available')
    }

    // Check module test directories
    for (const [module, moduleConfig] of Object.entries(this.config.modules)) {
      const testDir = path.join(PROJECT_ROOT, moduleConfig.testDir)
      try {
        await fs.access(testDir)
      }
      catch {
        issues.push(`Test directory missing for ${module}: ${moduleConfig.testDir}`)
      }
    }

    if (issues.length === 0) {
      console.log('✅ Test setup validation passed')
      return true
    }
    else {
      console.log('❌ Test setup validation failed:')
      issues.forEach(issue => console.log(`  - ${issue}`))
      return false
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2)
  const command = args[0]
  const runner = new TestRunner()

  // Check for verbose flag
  if (args.includes('--verbose') || args.includes('-v')) {
    runner.verbose = true
  }

  switch (command) {
    case 'all':
      const testType = args[1] || 'all'
      await runner.runAllTests(testType)
      break

    case 'module':
      if (args[1] && args[2]) {
        await runner.runModuleTests(args[1], args[2])
      }
      else if (args[1]) {
        await runner.runModuleTests(args[1])
      }
      else {
        console.log('Usage: run-tests.js module <module-name> [test-type]')
      }
      break

    case 'pattern':
      if (args[1]) {
        await runner.runSpecificTest(args[1])
      }
      else {
        console.log('Usage: run-tests.js pattern <test-pattern>')
      }
      break

    case 'validate':
      await runner.validateSetup()
      break

    case 'emulators':
      await runner.loadConfig()
      await runner.startFirebaseEmulators()
      break

    default:
      console.log(`
PIB Calendar Test Runner

Usage:
  node run-tests.js <command> [options]

Commands:
  all [type]                  Run all tests (optional: unit|integration|e2e|coverage)
  module <name> [type]        Run tests for specific module
  pattern <pattern>           Run tests matching pattern
  validate                    Validate test setup
  emulators                   Start Firebase emulators

Options:
  --verbose, -v               Enable verbose output

Examples:
  node run-tests.js all unit
  node run-tests.js module auth-module e2e
  node run-tests.js pattern "calendar"
  node run-tests.js validate --verbose
      `)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export default TestRunner
