# PIB Calendar Test Configuration

This directory contains the test configuration and management tools for the PIB Calendar project.

## Overview

The test configuration system provides unified testing across all modules and applications in the PIB Calendar monorepo. It supports multiple test types, environments, and provides centralized configuration management.

## Quick Start

### 1. Check Current Configuration
```bash
pnpm test:config
```

### 2. Configure Testing Mode
```bash
# Set to visible mode for debugging
pnpm test:config:mode visible

# Set to headless mode for CI/automation
pnpm test:config:mode headless
```

### 3. Configure Port
```bash
pnpm test:config:port 3007
```

### 4. Enable Credentials
```bash
pnpm test:config:credentials
```

### 5. Run Tests
```bash
# Run all tests
pnpm test:all

# Run specific test types
pnpm test:unit
pnpm test:integration
pnpm test:e2e

# Run tests for specific modules
pnpm test:auth
pnpm test:ai
pnpm test:calendar
```

## Files

### Configuration Files
- `test-config.json` - Main test configuration
- `.env.test.local.example` - Template for test environment variables
- `.env.test.local` - Actual test environment variables (gitignored)

### Management Scripts
- `test-config-manager.js` - Configuration management utility
- `run-tests.js` - Unified test runner
- `README.md` - This documentation

## Configuration Structure

The `test-config.json` file contains:

### Default Settings
- **Mode**: `headless` | `visible`
- **Port**: Default port for test server (3006)
- **Timeout**: Default timeout values
- **Retries**: Retry configuration
- **Workers**: Parallel execution settings

### Environments
- **Development**: Local development server
- **Testing**: Dedicated testing environment
- **Emulator**: Firebase emulator environment

### Module Configuration
Each module has specific test settings:
- Test directory paths
- Configuration files
- Coverage thresholds
- Test patterns

### Test Types
- **Unit**: Fast, isolated tests
- **Integration**: Component integration tests
- **E2E**: End-to-end browser tests
- **Performance**: Lighthouse performance tests
- **Security**: Security and accessibility tests

## Commands Reference

### Configuration Management
```bash
# View current configuration
node .claude/test-config-manager.js status

# Set testing mode
node .claude/test-config-manager.js mode <headless|visible>

# Set default port
node .claude/test-config-manager.js port <port-number>

# Enable credential storage
node .claude/test-config-manager.js credentials

# Validate configuration
node .claude/test-config-manager.js validate
```

### Test Runner
```bash
# Run all tests
node .claude/run-tests.js all [type]

# Run tests for specific module
node .claude/run-tests.js module <module-name> [type]

# Run tests matching pattern
node .claude/run-tests.js pattern <pattern>

# Validate test setup
node .claude/run-tests.js validate

# Start Firebase emulators
node .claude/run-tests.js emulators
```

### Package.json Scripts
```bash
# Test execution
pnpm test:all               # All tests
pnpm test:unit              # Unit tests only
pnpm test:integration       # Integration tests only
pnpm test:e2e              # E2E tests only
pnpm test:coverage         # Coverage reports

# Module-specific tests
pnpm test:auth             # Auth module tests
pnpm test:ai               # AI module tests
pnpm test:calendar         # Calendar-specific tests

# Configuration
pnpm test:config           # Show configuration
pnpm test:config:mode      # Set testing mode
pnpm test:config:port      # Set default port
pnpm test:config:credentials # Enable credentials

# Validation
pnpm test:validate         # Validate test setup
```

## Environment Setup

### 1. Copy Environment Template
```bash
cp .env.test.local.example .env.test.local
```

### 2. Configure Test Credentials
Edit `.env.test.local` with your test credentials:
- Firebase project settings
- Google API credentials
- Test user accounts
- API keys for AI services

### 3. Start Firebase Emulators
```bash
pnpm emulators
```

## Module Testing

### Auth Module
Tests authentication flows, Firebase integration, and Google Calendar/Gmail APIs:
```bash
pnpm test:auth
pnpm --filter=@pib/auth-module test:google-calendar
```

### AI Module
Tests AI chat functionality, WebSocket connections, and security:
```bash
pnpm test:ai
```

### Tairo Layer
Tests UI components and composables:
```bash
# Note: Tairo tests need to be created
cd layers/tairo && pnpm test
```

## Test Types

### Unit Tests
- **Runner**: Vitest
- **Pattern**: `**/*.{test,spec}.{js,ts,vue}`
- **Excludes**: E2E and integration tests
- **Timeout**: 5 seconds

### Integration Tests
- **Runner**: Vitest
- **Pattern**: `**/*.integration.{test,spec}.{js,ts}`
- **Setup**: Custom setup files
- **Timeout**: 15 seconds

### E2E Tests
- **Runner**: Playwright
- **Pattern**: `**/*.e2e.spec.ts`
- **Browsers**: Chrome, Firefox, Safari, Mobile
- **Timeout**: 30 seconds

### Performance Tests
- **Runner**: Lighthouse
- **Thresholds**: Performance 90%, Accessibility 95%
- **Timeout**: 60 seconds

### Security Tests
- **Tools**: axe-core, Firebase rules testing
- **Focus**: Accessibility, security vulnerabilities
- **Timeout**: 20 seconds

## Coverage Requirements

### Auth Module: 90%
- Composables, components, utils
- Excludes test files and node_modules

### AI Module: 90%
- Composables, components, server code
- Excludes test files and node_modules

### Tairo Layer: 85%
- Components and composables
- Excludes test files

## Debugging

### Enable Visible Mode
```bash
pnpm test:config:mode visible
```

### Verbose Output
```bash
node .claude/run-tests.js all --verbose
```

### Screenshots and Videos
- Screenshots: Captured on failure
- Videos: Disabled by default (enable in config)
- Traces: Captured on first retry

## CI/CD Configuration

The configuration includes CI-specific settings:
- **Retries**: 3 attempts
- **Workers**: 1 (for stability)
- **Artifacts**: Screenshots, videos, traces, coverage
- **Reporters**: GitHub Actions, HTML

## Firebase Emulators

### Ports
- **Auth**: 9099
- **Firestore**: 8080
- **Storage**: 9199
- **Functions**: 5001
- **UI**: 4000

### Rules
- **Firestore**: `./firestore.rules`
- **Storage**: `./storage.rules`

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   pnpm test:config:port 3007
   ```

2. **Missing credentials**
   ```bash
   pnpm test:config:credentials
   ```

3. **Emulator not starting**
   ```bash
   pnpm emulators
   # Check if ports are available
   ```

4. **Test directory missing**
   ```bash
   mkdir -p layers/module-name/tests
   ```

### Validation
```bash
# Validate configuration
node .claude/test-config-manager.js validate

# Validate test setup
node .claude/run-tests.js validate
```

## Best Practices

1. **Use appropriate test types**
   - Unit tests for isolated functionality
   - Integration tests for module interactions
   - E2E tests for user workflows

2. **Maintain coverage thresholds**
   - Auth and AI modules: 90%
   - Tairo layer: 85%

3. **Test with emulators**
   - Use Firebase emulators for consistent testing
   - Don't test against production services

4. **Credential security**
   - Never commit test credentials
   - Use environment variables
   - Rotate credentials regularly

5. **Parallel execution**
   - Enable for faster test runs
   - Disable for debugging

## Contributing

When adding new modules or tests:

1. Update `test-config.json` with module configuration
2. Add test directories and configuration files
3. Update package.json scripts if needed
4. Ensure coverage thresholds are met
5. Document any special setup requirements
