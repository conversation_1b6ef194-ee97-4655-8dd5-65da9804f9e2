# Test Configuration System

This directory contains the test configuration management system for automated testing with <PERSON>wright and other test frameworks.

## Files Overview

- **test-config.json** - Main configuration file containing all test settings
- **test-config-manager.js** - CLI tool for managing test configuration
- **playwright-config-helper.ts** - TypeScript helper for Playwright integration
- **test-utils.ts** - Test utilities and custom fixtures
- **.credentials.enc** - Encrypted test credentials (created when credentials are saved)

## Quick Start

### 1. Interactive Setup
```bash
node .claude/test-config-manager.js
```

### 2. Command Line Configuration
```bash
# Set testing mode
node .claude/test-config-manager.js --mode visible

# Set default port
node .claude/test-config-manager.js --port 3006

# Save credentials securely
node .claude/test-config-manager.js --save-credentials

# Show current configuration
node .claude/test-config-manager.js --show
```

## Configuration Structure

### Testing Settings
- **mode**: `headless` or `visible` - Controls browser visibility
- **port**: Default port for local development server
- **baseUrl**: Base URL for tests
- **timeout**: Various timeout configurations
- **retries**: Retry configuration for flaky tests

### Browser Settings
- **defaultBrowser**: `chromium`, `firefox`, or `webkit`
- **viewport**: Browser window dimensions
- **locale**: Browser locale (e.g., 'en-US')
- **timezone**: Browser timezone
- **permissions**: Browser permissions to grant
- **deviceScaleFactor**: Device pixel ratio
- **colorScheme**: `light` or `dark`

### Credentials
- Encrypted storage for test user credentials
- Support for multiple user types (testUser, adminUser, etc.)
- Secure encryption using AES-256-CBC

### Environments
Configure different environments:
- **local**: Local development
- **staging**: Staging environment
- **production**: Production environment

### Firebase Emulators
- Configure ports for Firebase emulators
- Enable/disable emulator usage

### Reporting
- **screenshots**: Configure screenshot capture
- **videos**: Enable/disable video recording
- **traces**: Enable/disable trace collection

## Using in Tests

### Basic Playwright Configuration
```typescript
// playwright.config.ts
import { defineConfig } from '@playwright/test'
import { PlaywrightConfigHelper } from './.claude/playwright-config-helper'

const configHelper = new PlaywrightConfigHelper(process.env.TEST_ENV || 'local')
export default defineConfig(configHelper.generatePlaywrightConfig())
```

### Using Test Utilities
```typescript
import { expect, test, TestHelpers } from '../.claude/test-utils'

test('example test', async ({ authenticatedPage }) => {
  // authenticatedPage is pre-authenticated based on saved credentials
  await authenticatedPage.goto('/dashboard')

  // Use helpers
  await TestHelpers.screenshot(authenticatedPage, 'dashboard')
  await TestHelpers.fillForm(authenticatedPage, { name: 'Test User' })
})
```

### Accessing Configuration in Tests
```typescript
import { testConfig } from './.claude/playwright-config-helper'

test('use configuration', async ({ page }) => {
  const config = testConfig.exportConfig()
  const baseURL = config.baseURL
  const credentials = config.credentials

  // Use configuration values
  await page.goto(baseURL)
})
```

## Security Notes

1. **Credentials are encrypted** using AES-256-CBC encryption
2. **Never commit .credentials.enc** to version control
3. **Use environment-specific configurations** for different deployment stages
4. **Rotate test credentials regularly**

## Environment Variables

- `TEST_ENV` - Specify which environment to use (local, staging, production)
- `CI` - Set to true in CI environments to disable server reuse

## Troubleshooting

### Configuration Not Found
```bash
Error: Test configuration not found
```
**Solution**: Run `node .claude/test-config-manager.js` to create the configuration

### Invalid Credentials
```bash
Error decrypting credentials
```
**Solution**: Re-save credentials using `--save-credentials` flag

### Port Already in Use
```bash
Error: Port 3000 is already in use
```
**Solution**: Change the port using `--port` flag or kill the process using the port

## Best Practices

1. **Use visible mode during development** for easier debugging
2. **Use headless mode in CI** for faster execution
3. **Configure appropriate timeouts** based on your application's performance
4. **Enable retries for flaky tests** but investigate the root cause
5. **Use different credentials for different test scenarios**
6. **Keep test configuration in sync** with your application's requirements

## Advanced Usage

### Custom Test Fixtures
```typescript
// Extend test fixtures
export const test = base.extend({
  customFixture: async ({ page }, use) => {
    // Setup
    await use(page)
    // Teardown
  }
})
```

### Multiple Browsers
Configure multiple browser projects in your Playwright config to test across different browsers automatically.

### Parallel Execution
Adjust the `workers` setting to control parallel test execution based on your machine's capabilities.

## Integration with CI/CD

Example GitHub Actions configuration:
```yaml
- name: Run Tests
  env:
    TEST_ENV: staging
  run: |
    npm install
    npx playwright install
    npm test
```

## Support

For issues or questions:
1. Check the configuration with `--show` flag
2. Verify credentials are properly saved
3. Ensure all required dependencies are installed
4. Check that Firebase emulators are running if configured
