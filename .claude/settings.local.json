{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__zen__*", "mcp__firecrawl__*", "mcp__playwright__*", "mcp__perplexity__*", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "mcp__zen__analyze", "mcp__zen__thinkdeep", "mcp__perplexity__search", "mcp__zen__testgen", "<PERSON><PERSON>(chmod:*)", "Bash(./sync-pib-to-projects.sh:*)", "Bash(./sync-pib-to-projects-simple.sh:*)", "Bash(grep:*)", "<PERSON><PERSON>(cat:*)", "Bash(/Users/<USER>/Projects/PIB-METHOD/.claude/hooks/auto-review-agent.sh:*)", "Bash(bash:*)", "mcp__Firecrawl__firecrawl_scrape", "<PERSON><PERSON>(mkdir:*)", "Bash(node:*)", "mcp__zen__planner", "Bash(/.claude/hooks/worktree-manager.sh:*)", "<PERSON><PERSON>(./.claude/hooks/worktree-manager.sh:*)", "Bash(./.claude/hooks/state-isolator.sh:*)", "Bash(./sync-pib-complete.sh:*)", "Bash(./.claude/commands/workflows/feature-start calendar upgrade)", "Bash(./.claude/commands/workflows/feature-list:*)", "Bash(./.claude/commands/workflows/feature-cleanup:*)", "Bash(./.claude/commands/workflows/feature-start test-inside:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git worktree remove:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(python3:*)", "Bash(gh auth:*)", "Bash(git tag:*)", "Bash(git remote add:*)", "Bash(gh repo view:*)", "Bash(git push:*)", "mcp__playwright__playwright_navigate", "<PERSON><PERSON>(npx playwright:*)", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_select", "mcp__playwright__playwright_get_visible_html", "mcp__playwright__playwright_click", "mcp__playwright__playwright_console_logs", "mcp__playwright__playwright_evaluate", "mcp__playwright__playwright_close", "<PERSON><PERSON>(sh:*)", "Bash(rg:*)", "Bash(for:*)", "Bash(do echo \"Processing $file\")", "Bash(done)", "Bash(do if grep -q \"PIB\" \"$file\")", "Bash(then echo \"Processing $file\")", "Bash(fi)", "Bash(do if [ -f \"$file\" ])", "Bash(do sed -i '' 's/PIB/PIB/g' \"$file\")", "Bash(./.claude/commands/workflows/feature-start:*)", "mcp__playwright__start_codegen_session", "Bash(./sync-pib-rsync.sh:*)", "Bash(do)", "Bash(if [ -d \"$dir/.claude\" ])", "<PERSON><PERSON>(then)", "Bash(echo \"Copying to $dir\")", "Bash(if [ -d \"$dir\" ])", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(realpath:*)", "Bash(pnpm build:*)", "Bash(pnpm dev:*)", "Bash(pnpm typecheck:*)", "Bash(pnpm nuxi:*)", "Bash(npx tsc:*)", "Bash(pnpm lint:*)", "Bash(pnpm:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(git remote:*)", "Bash(git branch:*)", "mcp__zen__debug"], "deny": []}}