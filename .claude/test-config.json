{"version": "1.0.0", "lastUpdated": "2025-07-06T18:41:26.046Z", "project": {"name": "pib-Calendar", "type": "nuxt-monorepo", "framework": "Vue3 + Nuxt3 + TypeScript"}, "defaults": {"mode": "visible", "port": 3003, "timeout": 30000, "retries": 2, "workers": "auto"}, "testingMode": "visible", "defaultPort": 3003, "browser": {"type": "chromium", "viewport": {"width": 1280, "height": 720}, "userAgent": "PIB-Calendar-Test/1.0.0", "headless": false, "devices": {"desktop": "Desktop Chrome", "mobile": "iPhone 12", "tablet": "iPad Pro"}, "screenshots": {"mode": "only-on-failure", "quality": 90}, "trace": {"mode": "on-first-retry", "screenshots": true, "snapshots": true}}, "timeouts": {"navigation": 30000, "element": 10000, "assertion": 5000, "test": 30000}, "retryConfig": {"maxRetries": 2, "retryDelay": 1000}, "environments": {"development": {"port": 3003, "baseUrl": "http://localhost:3003", "description": "Local development server"}, "testing": {"port": 3007, "baseUrl": "http://localhost:3007", "description": "Dedicated testing environment"}, "emulator": {"port": 3008, "baseUrl": "http://localhost:3008", "description": "Firebase emulator environment", "firebase": {"auth": "http://localhost:9099", "firestore": "http://localhost:8080", "storage": "http://localhost:9199", "functions": "http://localhost:5001"}}}, "environment": {"baseUrl": "http://localhost:3003", "useEmulators": true, "emulatorPorts": {"auth": 9099, "firestore": 8080, "storage": 9199, "functions": 5001, "ui": 4000}}, "modules": {"auth-module": {"testDir": "./layers/auth-module/tests", "configFile": "./layers/auth-module/playwright.config.ts", "coverage": {"threshold": 90, "include": ["composables/**", "components/**", "utils/**"], "exclude": ["**/*.test.ts", "**/*.spec.ts", "**/node_modules/**"]}, "e2e": {"patterns": ["auth-journey.spec.ts", "gmail-inbox-workflows.spec.ts", "google-calendar-workflows.spec.ts"]}}, "ai-module": {"testDir": "./layers/ai-module/tests", "configFile": "./layers/ai-module/playwright.config.ts", "coverage": {"threshold": 90, "include": ["composables/**", "components/**", "server/**"], "exclude": ["**/*.test.ts", "**/*.spec.ts", "**/node_modules/**"]}}, "tairo": {"testDir": "./layers/tairo/tests", "coverage": {"threshold": 85, "include": ["components/**", "composables/**"], "exclude": ["**/*.test.ts", "**/*.spec.ts"]}}}, "testTypes": {"unit": {"runner": "vitest", "timeout": 5000, "pattern": "**/*.{test,spec}.{js,ts,vue}", "exclude": ["**/*.e2e.spec.ts", "**/*.integration.test.ts"]}, "integration": {"runner": "vitest", "timeout": 15000, "pattern": "**/*.integration.{test,spec}.{js,ts}", "setupFiles": ["./tests/setup.ts"]}, "e2e": {"runner": "playwright", "timeout": 30000, "pattern": "**/*.e2e.spec.ts", "globalSetup": "./tests/global-setup.ts"}, "performance": {"runner": "lighthouse", "timeout": 60000, "thresholds": {"performance": 90, "accessibility": 95, "bestPractices": 90, "seo": 90}}, "security": {"runner": "custom", "timeout": 20000, "tools": ["axe-core", "firebase-rules-test"]}}, "credentials": {"enabled": true, "encrypted": false, "storage": {"type": "env", "file": ".env.test.local"}, "testUser": {"email": "<EMAIL>", "password": "MPStander@3"}, "variables": ["GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "FIREBASE_PROJECT_ID", "FIREBASE_API_KEY", "TEST_USER_EMAIL", "TEST_USER_PASSWORD"]}, "screenshots": {"onFailure": true, "directory": "./test-results/screenshots", "format": "png", "quality": 90}, "video": {"enabled": false, "directory": "./test-results/videos", "format": "webm"}, "commands": {"test:all": "pnpm run test", "test:unit": "pnpm --filter=\"*\" test:unit", "test:integration": "pnpm --filter=\"*\" test:integration", "test:e2e": "pnpm --filter=\"*\" test:e2e", "test:coverage": "pnpm --filter=\"*\" test:coverage", "test:auth": "pnpm --filter=@pib/auth-module test", "test:ai": "pnpm --filter=@pib/ai-module test", "test:calendar": "pnpm --filter=@pib/auth-module test:google-calendar", "test:security": "pnpm --filter=\"*\" test:security", "test:performance": "pnpm --filter=\"*\" test:performance"}, "parallel": {"enabled": true, "maxWorkers": 4, "fullyParallel": false, "shardMode": "module"}, "reporting": {"format": "html", "outputDir": "./test-results", "open": "never", "video": "retain-on-failure", "trace": "retain-on-failure"}, "firebase": {"emulators": {"enabled": true, "config": "./firebase.json", "ports": {"auth": 9099, "firestore": 8080, "storage": 9199, "functions": 5001, "ui": 4000}, "rules": {"firestore": "./firestore.rules", "storage": "./storage.rules"}}}, "ci": {"enabled": true, "retries": 3, "workers": 1, "forbidOnly": true, "reporter": ["github", "html"], "artifacts": {"screenshots": true, "videos": true, "traces": true, "coverage": true}}, "debugging": {"slowMo": 0, "devtools": false, "headful": false, "video": "retain-on-failure", "inspector": false}}