{"permissions": {"allow": ["<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(node build-web-agent.js)", "Bash(ls:*)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:github.com)", "Bash(find:*)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(.claude/test-hooks.sh:*)", "Bash(./sync-pib-complete.sh:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(pnpm lint:*)", "Bash(pnpm run lint:*)", "Bash(pnpm typecheck:*)", "Bash(eslint:*)", "Bash(vue-tsc:*)"], "deny": []}, "hooks": {"PreToolUse": [{"matcher": "^(Write|Edit|MultiEdit)$", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" read-workflow.sh || exit 2'"}]}, {"matcher": "^(exit_plan_mode|TodoWrite)$", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" lever-planning-reminder.sh || exit 2'"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}, {"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" confirmation-notifier.sh || exit 2'"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" workflow-transition.sh || exit 2'"}, {"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}]}]}}