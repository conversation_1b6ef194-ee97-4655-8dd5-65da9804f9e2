#!/usr/bin/env node
/**
 * PIB Calendar Test Configuration Manager
 *
 * This script helps manage test configurations, environment setup,
 * and credential management for the PIB Calendar project.
 */

import fs from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const PROJECT_ROOT = path.resolve(__dirname, '..')
const CONFIG_FILE = path.join(__dirname, 'test-config.json')

class TestConfigManager {
  constructor() {
    this.config = null
  }

  async loadConfig() {
    try {
      const configData = await fs.readFile(CONFIG_FILE, 'utf8')
      this.config = JSON.parse(configData)
      return this.config
    }
    catch (error) {
      console.error('Failed to load test configuration:', error.message)
      process.exit(1)
    }
  }

  async updateMode(mode) {
    if (!['headless', 'visible'].includes(mode)) {
      console.error('Invalid mode. Use "headless" or "visible"')
      return false
    }

    await this.loadConfig()
    this.config.testingMode = mode
    this.config.browser.headless = mode === 'headless'
    this.config.defaults.mode = mode

    await this.saveConfig()
    console.log(`✅ Testing mode updated to: ${mode}`)
    return true
  }

  async updatePort(port) {
    const portNum = Number.parseInt(port)
    if (isNaN(portNum) || portNum < 1000 || portNum > 65535) {
      console.error('Invalid port. Use a number between 1000-65535')
      return false
    }

    await this.loadConfig()
    this.config.defaultPort = portNum
    this.config.defaults.port = portNum
    this.config.environment.baseUrl = `http://localhost:${portNum}`
    this.config.environments.development.port = portNum
    this.config.environments.development.baseUrl = `http://localhost:${portNum}`

    await this.saveConfig()
    console.log(`✅ Default port updated to: ${portNum}`)
    return true
  }

  async enableCredentials() {
    await this.loadConfig()
    this.config.credentials.enabled = true

    // Check if .env.test.local exists
    const envFile = path.join(PROJECT_ROOT, '.env.test.local')
    try {
      await fs.access(envFile)
      console.log('✅ Using existing .env.test.local file')
    }
    catch {
      console.log('📝 Creating .env.test.local from example...')
      const exampleFile = path.join(PROJECT_ROOT, '.env.test.local.example')
      try {
        await fs.copyFile(exampleFile, envFile)
        console.log('✅ Created .env.test.local - please fill in your credentials')
      }
      catch (error) {
        console.error('Failed to create .env.test.local:', error.message)
        return false
      }
    }

    await this.saveConfig()
    console.log('✅ Credential storage enabled')
    return true
  }

  async saveConfig() {
    this.config.lastUpdated = new Date().toISOString()
    await fs.writeFile(CONFIG_FILE, JSON.stringify(this.config, null, 2))
  }

  async getStatus() {
    await this.loadConfig()

    console.log('\n📊 PIB Calendar Test Configuration Status')
    console.log('='.repeat(50))
    console.log(`Mode: ${this.config.testingMode}`)
    console.log(`Port: ${this.config.defaultPort}`)
    console.log(`Base URL: ${this.config.environment.baseUrl}`)
    console.log(`Credentials: ${this.config.credentials.enabled ? '✅ Enabled' : '❌ Disabled'}`)
    console.log(`Firebase Emulators: ${this.config.environment.useEmulators ? '✅ Enabled' : '❌ Disabled'}`)
    console.log(`Browser: ${this.config.browser.type} (${this.config.browser.headless ? 'headless' : 'visible'})`)
    console.log(`Parallel Testing: ${this.config.parallel.enabled ? '✅ Enabled' : '❌ Disabled'}`)
    console.log(`Max Workers: ${this.config.parallel.maxWorkers}`)
    console.log(`Last Updated: ${this.config.lastUpdated}`)

    console.log('\n🧪 Available Test Commands:')
    Object.entries(this.config.commands).forEach(([key, command]) => {
      console.log(`  ${key}: ${command}`)
    })

    console.log('\n📁 Module Test Directories:')
    Object.entries(this.config.modules).forEach(([module, config]) => {
      console.log(`  ${module}: ${config.testDir}`)
    })
  }

  async validate() {
    await this.loadConfig()
    const issues = []

    // Check if test directories exist
    for (const [moduleName, moduleConfig] of Object.entries(this.config.modules)) {
      const testDir = path.join(PROJECT_ROOT, moduleConfig.testDir)
      try {
        await fs.access(testDir)
      }
      catch {
        issues.push(`Test directory missing for ${moduleName}: ${moduleConfig.testDir}`)
      }
    }

    // Check if required config files exist
    for (const [moduleName, moduleConfig] of Object.entries(this.config.modules)) {
      if (moduleConfig.configFile) {
        const configFile = path.join(PROJECT_ROOT, moduleConfig.configFile)
        try {
          await fs.access(configFile)
        }
        catch {
          issues.push(`Config file missing for ${moduleName}: ${moduleConfig.configFile}`)
        }
      }
    }

    // Check credentials setup
    if (this.config.credentials.enabled) {
      const envFile = path.join(PROJECT_ROOT, '.env.test.local')
      try {
        await fs.access(envFile)
      }
      catch {
        issues.push('Credentials enabled but .env.test.local file not found')
      }
    }

    if (issues.length === 0) {
      console.log('✅ Test configuration validation passed')
    }
    else {
      console.log('❌ Test configuration validation failed:')
      issues.forEach(issue => console.log(`  - ${issue}`))
    }

    return issues.length === 0
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2)
  const command = args[0]
  const manager = new TestConfigManager()

  switch (command) {
    case 'mode':
      if (args[1]) {
        await manager.updateMode(args[1])
      }
      else {
        console.log('Usage: test-config-manager.js mode <headless|visible>')
      }
      break

    case 'port':
      if (args[1]) {
        await manager.updatePort(args[1])
      }
      else {
        console.log('Usage: test-config-manager.js port <port-number>')
      }
      break

    case 'credentials':
      await manager.enableCredentials()
      break

    case 'status':
      await manager.getStatus()
      break

    case 'validate':
      await manager.validate()
      break

    default:
      console.log(`
PIB Calendar Test Configuration Manager

Usage:
  node test-config-manager.js <command> [options]

Commands:
  mode <headless|visible>     Set testing mode
  port <number>               Set default port
  credentials                 Enable credential storage
  status                      Show current configuration
  validate                    Validate configuration

Examples:
  node test-config-manager.js mode visible
  node test-config-manager.js port 3007
  node test-config-manager.js credentials
  node test-config-manager.js status
      `)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export default TestConfigManager
