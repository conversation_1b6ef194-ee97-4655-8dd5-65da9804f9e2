import type { PlaywrightTestConfig } from '@playwright/test'
import * as crypto from 'node:crypto'
import * as fs from 'node:fs'
import * as path from 'node:path'

/**
 * Playwright Configuration Helper
 * Loads test configuration from .claude/test-config.json
 */

interface TestConfig {
  testing: {
    mode: 'headless' | 'visible'
    port: number
    baseUrl: string
    timeout: {
      default: number
      navigation: number
      assertion: number
    }
    retries: {
      enabled: boolean
      count: number
      onlyFailures: boolean
    }
  }
  browser: {
    defaultBrowser: 'chromium' | 'firefox' | 'webkit'
    viewport: {
      width: number
      height: number
    }
    userAgent?: string
    locale: string
    timezone: string
    permissions: string[]
    deviceScaleFactor: number
    isMobile: boolean
    hasTouch: boolean
    colorScheme: 'light' | 'dark'
  }
  credentials: {
    enabled: boolean
    storageType: string
    path: string
  }
  environments: {
    [key: string]: {
      baseUrl: string
      port?: number
    }
  }
  firebase: {
    useEmulator: boolean
    emulatorPorts: {
      auth: number
      firestore: number
      storage: number
      functions: number
    }
  }
  reporting: {
    screenshots: {
      onFailure: boolean
      fullPage: boolean
      path: string
    }
    videos: {
      enabled: boolean
      path: string
    }
    traces: {
      enabled: boolean
      path: string
    }
  }
  parallelization: {
    workers: number
    fullyParallel: boolean
    forbidOnly: boolean
  }
}

export class PlaywrightConfigHelper {
  private config: TestConfig
  private environment: string

  constructor(environment: string = 'local') {
    this.environment = environment
    this.config = this.loadConfig()
  }

  private loadConfig(): TestConfig {
    const configPath = path.join(__dirname, 'test-config.json')

    if (!fs.existsSync(configPath)) {
      throw new Error(`Test configuration not found at ${configPath}. Run test-config-manager.js to create it.`)
    }

    try {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'))
    }
    catch (error) {
      throw new Error(`Failed to parse test configuration: ${error.message}`)
    }
  }

  /**
   * Load encrypted credentials
   */
  loadCredentials(): any {
    if (!this.config.credentials.enabled) {
      return null
    }

    const credPath = this.config.credentials.path
    if (!fs.existsSync(credPath)) {
      return null
    }

    try {
      const encrypted = JSON.parse(fs.readFileSync(credPath, 'utf8'))
      const algorithm = 'aes-256-cbc'
      const key = crypto.scryptSync('test-config-secret', 'salt', 32)
      const iv = Buffer.from(encrypted.iv, 'hex')

      const decipher = crypto.createDecipheriv(algorithm, key, iv)
      let decrypted = decipher.update(encrypted.data, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return JSON.parse(decrypted)
    }
    catch (error) {
      console.error('Failed to decrypt credentials:', error)
      return null
    }
  }

  /**
   * Generate Playwright configuration based on test-config.json
   */
  generatePlaywrightConfig(): PlaywrightTestConfig {
    const envConfig = this.config.environments[this.environment]
    const baseURL = envConfig?.baseUrl || this.config.testing.baseUrl

    return {
      testDir: './tests',
      fullyParallel: this.config.parallelization.fullyParallel,
      forbidOnly: this.config.parallelization.forbidOnly,
      retries: this.config.testing.retries.enabled ? this.config.testing.retries.count : 0,
      workers: this.config.parallelization.workers,
      reporter: [
        ['html'],
        ['list'],
        ['json', { outputFile: 'test-results/results.json' }],
      ],
      use: {
        baseURL,
        trace: this.config.reporting.traces.enabled ? 'on' : 'off',
        video: this.config.reporting.videos.enabled ? 'on' : 'off',
        screenshot: this.config.reporting.screenshots.onFailure ? 'only-on-failure' : 'off',
        headless: this.config.testing.mode === 'headless',
        viewport: this.config.browser.viewport,
        locale: this.config.browser.locale,
        timezoneId: this.config.browser.timezone,
        permissions: this.config.browser.permissions,
        deviceScaleFactor: this.config.browser.deviceScaleFactor,
        isMobile: this.config.browser.isMobile,
        hasTouch: this.config.browser.hasTouch,
        colorScheme: this.config.browser.colorScheme,
        userAgent: this.config.browser.userAgent,
        navigationTimeout: this.config.testing.timeout.navigation,
        actionTimeout: this.config.testing.timeout.assertion,
      },
      projects: [
        {
          name: this.config.browser.defaultBrowser,
          use: {
            ...this.getBrowserOptions(this.config.browser.defaultBrowser),
          },
        },
      ],
      outputDir: 'test-results',
      webServer: envConfig?.port
        ? {
            command: `npm run dev -- --port ${envConfig.port}`,
            port: envConfig.port,
            timeout: 120 * 1000,
            reuseExistingServer: !process.env.CI,
          }
        : undefined,
    }
  }

  /**
   * Get browser-specific options
   */
  private getBrowserOptions(browser: string) {
    const baseOptions = {
      viewport: this.config.browser.viewport,
      locale: this.config.browser.locale,
      timezoneId: this.config.browser.timezone,
    }

    switch (browser) {
      case 'chromium':
        return {
          ...baseOptions,
          channel: 'chrome',
        }
      case 'firefox':
        return baseOptions
      case 'webkit':
        return baseOptions
      default:
        return baseOptions
    }
  }

  /**
   * Get Firebase emulator configuration
   */
  getFirebaseConfig() {
    if (!this.config.firebase.useEmulator) {
      return null
    }

    return {
      useEmulator: true,
      emulatorPorts: this.config.firebase.emulatorPorts,
      emulatorHost: 'localhost',
    }
  }

  /**
   * Get test user credentials
   */
  getTestUser(userType: string = 'testUser') {
    const credentials = this.loadCredentials()
    return credentials?.[userType] || null
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig() {
    return this.config.environments[this.environment] || this.config.environments.local
  }

  /**
   * Export configuration for use in tests
   */
  exportConfig() {
    return {
      baseURL: this.getEnvironmentConfig().baseUrl,
      credentials: this.loadCredentials(),
      firebase: this.getFirebaseConfig(),
      browser: this.config.browser,
      timeouts: this.config.testing.timeout,
      reporting: this.config.reporting,
    }
  }
}

// Export singleton instance
export const testConfig = new PlaywrightConfigHelper(process.env.TEST_ENV || 'local')
