name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '22'
  PNPM_VERSION: '8'

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20, 22]

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run unit tests
        run: pnpm test:unit

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./layers/*/coverage/lcov.info
          flags: unit
          name: unit-tests-${{ matrix.node-version }}

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    services:
      firebase:
        image: firebase/emulators
        ports:
          - 9099:9099 # Auth
          - 8080:8080 # Firestore
          - 9199:9199 # Storage
        options: >-
          --health-cmd "curl -f http://localhost:4000 || exit 1"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup Firebase emulators
        run: |
          npx firebase-tools emulators:exec --only auth,firestore,storage --project test-project "echo 'Emulators started'"

      - name: Run integration tests
        run: pnpm test:integration
        env:
          NUXT_PUBLIC_USE_FIREBASE_EMULATOR: true
          FIREBASE_AUTH_EMULATOR_HOST: localhost:9099
          FIRESTORE_EMULATOR_HOST: localhost:8080

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./layers/*/coverage/lcov.info
          flags: integration
          name: integration-tests

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Playwright browsers
        run: pnpm exec playwright install --with-deps ${{ matrix.browser }}

      - name: Build application
        run: pnpm build

      - name: Run E2E tests
        run: pnpm test:e2e --project=${{ matrix.browser }}

      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: e2e-failures-${{ matrix.browser }}
          path: |
            test-results/
            playwright-report/

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run security tests
        run: pnpm test:security

      - name: Run dependency audit
        run: pnpm audit --audit-level=high

      - name: Run OWASP dependency check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: PIB
          path: .
          format: HTML

      - name: Upload security report
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: reports/

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run performance tests
        run: pnpm test:performance

      - name: Analyze bundle size
        run: |
          pnpm build
          npx bundlesize

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-metrics
          path: performance-results/

  coverage-check:
    name: Coverage Requirements
    needs: [unit-tests, integration-tests]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Download coverage reports
        uses: actions/download-artifact@v3

      - name: Check coverage thresholds
        run: |
          # Check if coverage meets 90% threshold
          total_coverage=$(lcov-summary coverage/lcov.info | grep "Total:" | awk '{print $2}' | sed 's/%//')
          if (( $(echo "$total_coverage < 90" | bc -l) )); then
            echo "Coverage is below 90% threshold: $total_coverage%"
            exit 1
          fi
          echo "Coverage meets requirements: $total_coverage%"

  lighthouse:
    name: Lighthouse CI
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build application
        run: pnpm build

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            http://localhost:3000
            http://localhost:3000/auth/login
            http://localhost:3000/dashboard
          budgetPath: ./lighthouse-budget.json
          uploadArtifacts: true
          temporaryPublicStorage: true
