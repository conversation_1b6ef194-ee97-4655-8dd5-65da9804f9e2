name: Comprehensive Testing Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run nightly performance tests
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '22'
  PNPM_VERSION: 10.5.2

jobs:
  setup:
    name: Setup and Dependencies
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-keys.outputs.cache-key }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Generate cache keys
        id: cache-keys
        run: |
          echo "cache-key=pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ steps.cache-keys.outputs.cache-key }}
          restore-keys: |
            pnpm-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

  lint-and-typecheck:
    name: Lint and Type Check
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Run ESLint
        run: pnpm lint

      - name: Run TypeScript type checking
        run: pnpm typecheck

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: setup
    strategy:
      matrix:
        module: [auth-module, shared-utils, shared-types, ai-module, crm]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Run unit tests for ${{ matrix.module }}
        run: |
          cd layers/${{ matrix.module }}
          if [ -f "vitest.config.ts" ]; then
            pnpm test:unit
          else
            echo "No unit tests configured for ${{ matrix.module }}"
          fi

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: unit-test-results-${{ matrix.module }}
          path: |
            layers/${{ matrix.module }}/test-results.json
            layers/${{ matrix.module }}/coverage/

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: setup
    services:
      firebase-emulator:
        image: node:22
        ports:
          - 9099:9099 # Auth emulator
          - 8080:8080 # Firestore emulator
          - 9199:9199 # Storage emulator
          - 5001:5001 # Functions emulator
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Start Firebase Emulators
        run: |
          cd layers/auth-module
          firebase emulators:start --only auth,firestore,storage,functions --project test-project &
          sleep 10  # Wait for emulators to start
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

      - name: Run integration tests
        run: |
          cd layers/auth-module
          pnpm test:integration
        env:
          NUXT_PUBLIC_USE_FIREBASE_EMULATOR: true
          FIREBASE_AUTH_EMULATOR_HOST: localhost:9099
          FIRESTORE_EMULATOR_HOST: localhost:8080

      - name: Upload integration test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: integration-test-results
          path: layers/auth-module/integration-results.json

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Run security audit
        run: pnpm audit --audit-level moderate

      - name: Run security tests
        run: |
          cd layers/auth-module
          pnpm test:security

      - name: Run dependency vulnerability scan
        uses: actions/dependency-review-action@v4
        if: github.event_name == 'pull_request'

      - name: Upload security test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-test-results
          path: layers/auth-module/security-results.json

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: setup
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install Playwright browsers
        run: |
          cd layers/auth-module
          npx playwright install ${{ matrix.browser }}

      - name: Start development server
        run: |
          cd layers/auth-module
          pnpm dev &
          sleep 30  # Wait for server to start
        env:
          NODE_ENV: test

      - name: Run E2E tests
        run: |
          cd layers/auth-module
          npx playwright test --project=${{ matrix.browser }}
        env:
          CI: true

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results-${{ matrix.browser }}
          path: |
            layers/auth-module/test-results/
            layers/auth-module/playwright-report/

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: setup
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[performance]')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Run performance tests
        run: |
          cd layers/auth-module
          pnpm test:performance

      - name: Upload performance test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-test-results
          path: layers/auth-module/performance-results.json

  coverage-report:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Download test results
        uses: actions/download-artifact@v4
        with:
          pattern: '*-test-results*'
          merge-multiple: true

      - name: Generate comprehensive coverage report
        run: |
          cd layers/auth-module
          pnpm test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          files: |
            layers/auth-module/coverage/lcov.info
            layers/shared-utils/coverage/lcov.info
            layers/shared-types/coverage/lcov.info
          fail_ci_if_error: true
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Check coverage thresholds
        run: |
          cd layers/auth-module
          node -e "
            const coverage = require('./coverage/coverage-summary.json');
            const thresholds = { lines: 90, branches: 90, functions: 90, statements: 90 };
            let failed = false;

            Object.entries(thresholds).forEach(([metric, threshold]) => {
              const actual = coverage.total[metric].pct;
              console.log(\`\${metric}: \${actual}% (threshold: \${threshold}%)\`);
              if (actual < threshold) {
                console.error(\`❌ \${metric} coverage \${actual}% is below threshold \${threshold}%\`);
                failed = true;
              } else {
                console.log(\`✅ \${metric} coverage meets threshold\`);
              }
            });

            if (failed) process.exit(1);
          "

  accessibility-tests:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install Playwright
        run: |
          cd layers/auth-module
          npx playwright install chromium

      - name: Start development server
        run: |
          cd layers/auth-module
          pnpm dev &
          sleep 30
        env:
          NODE_ENV: test

      - name: Run accessibility tests
        run: |
          cd layers/auth-module
          npx playwright test tests/accessibility/ --project=chromium

      - name: Upload accessibility test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: accessibility-test-results
          path: layers/auth-module/accessibility-report/

  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    needs: setup
    strategy:
      matrix:
        environment: [development, production]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Restore dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pnpm-store
            **/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Build application
        run: pnpm build
        env:
          NODE_ENV: ${{ matrix.environment }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        if: matrix.environment == 'production'
        with:
          name: production-build
          path: |
            .output/
            dist/

  final-report:
    name: Generate Final Report
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck, unit-tests, integration-tests, security-tests, e2e-tests, coverage-report, accessibility-tests, build-test]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Download all test results
        uses: actions/download-artifact@v4
        with:
          pattern: '*-test-results*'
          merge-multiple: true

      - name: Generate comprehensive test report
        run: |
          node scripts/generate-test-report.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Comment PR with test results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            if (fs.existsSync('test-report.json')) {
              const report = JSON.parse(fs.readFileSync('test-report.json', 'utf8'));

              const comment = `
              ## 🧪 Test Results Summary

              | Test Type | Passed | Failed | Coverage |
              |-----------|--------|--------|----------|
              | Unit Tests | ${report.unit?.passed || 0} | ${report.unit?.failed || 0} | - |
              | Integration Tests | ${report.integration?.passed || 0} | ${report.integration?.failed || 0} | - |
              | E2E Tests | ${report.e2e?.passed || 0} | ${report.e2e?.failed || 0} | - |
              | Security Tests | ${report.security?.passed || 0} | ${report.security?.failed || 0} | - |

              **Overall Coverage:** ${report.coverage?.lines || 0}% lines, ${report.coverage?.branches || 0}% branches

              **Status:** ${report.summary?.successRate >= 95 ? '✅ PASS' : '❌ FAIL'}
              `;

              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

      - name: Upload final report
        uses: actions/upload-artifact@v4
        with:
          name: comprehensive-test-report
          path: |
            test-report.json
            test-report.html

  deploy-preview:
    name: Deploy Preview
    runs-on: ubuntu-latest
    needs: [build-test, final-report]
    if: github.event_name == 'pull_request' && needs.final-report.result == 'success'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: production-build

      - name: Deploy to preview environment
        run: |
          echo "Deploy to preview environment would happen here"
          # Integration with deployment service (Vercel, Netlify, etc.)

      - name: Comment PR with preview URL
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const comment = `
            ## 🚀 Preview Deployment

            Your changes have been deployed to a preview environment:

            🔗 **Preview URL:** https://preview-pr-${context.issue.number}.your-domain.com

            This preview will be updated automatically with new commits.
            `;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
