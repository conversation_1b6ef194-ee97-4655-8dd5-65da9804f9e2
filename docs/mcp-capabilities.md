# MCP Capabilities Matrix

This document provides a comprehensive reference for PIB agents on available MCP (Model Context Protocol) tools and their optimal usage patterns.

## Available MCP Tools Overview

| MCP Tool | Type | Primary Use Cases | Key Capabilities |
|----------|------|------------------|------------------|
| **Zen** | AI Development | Multi-model analysis, code review, debugging | thinkdeep, analyze, chat, codereview, debug, testgen, precommit |
| **Context7** | Documentation | Library docs, API references | Real-time documentation retrieval, framework guides |
| **Perplexity** | Research | Market research, trends, competitive analysis | AI-powered search, real-time web analysis |
| **Firecrawl** | Content Extraction | Web scraping, competitive intelligence | Structured content extraction, data gathering |
| **Playwright** | Testing | Browser automation, E2E testing | Web testing, screenshots, user flow automation |

## Agent-to-MCP Mapping

### Architect Agent
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Zen** (Primary) | Complex system design, architectural analysis, multi-model validation | Technology selection, design validation, risk assessment |
| **Context7** | Technology research, framework documentation | API research, architectural patterns, best practices |

**Workflow Integration:**
- **Technology Selection**: Context7 → research options → Zen → multi-model validation
- **Architecture Design**: Context7 → best practices → Zen thinkdeep → complex decisions
- **Risk Assessment**: Zen → multi-model analysis → comprehensive risk identification

### Analyst Agent (Research Focus)
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Perplexity** (Primary) | Market research, industry trends, competitive intelligence | External knowledge gathering, market validation |
| **Firecrawl** | Competitive analysis, content extraction | Competitor data, industry resources |
| **Context7** | Technical research, feasibility assessment | Technology trends, framework analysis |
| **Zen** | Research validation, analysis synthesis | Multi-model validation, insight generation |

**Workflow Integration:**
- **Primary Research**: Perplexity → broad market analysis → trend identification
- **Competitive Analysis**: Firecrawl → competitor data → Perplexity → market context
- **Technical Research**: Context7 → framework docs → Zen → feasibility validation
- **Research Validation**: Multi-MCP findings → Zen → cross-validation

### Developer Agent (James)
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Zen** (Primary) | Code analysis, debugging, quality validation | Development workflow, code review, issue resolution |
| **Context7** | Just-in-time documentation, API references | Implementation guidance, pattern research |

**Workflow Integration:**
- **Before Implementation**: Context7 → research patterns → LEVER compliance check
- **During Development**: Context7 → API docs → Zen debug → issue resolution
- **Code Quality**: Zen codereview → multi-model validation → PIB standards
- **Before Commits**: Zen precommit → architectural consistency validation

### QA Tester Agent
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Playwright** (Primary) | Browser automation, E2E testing, visual testing | Web application testing, user flow validation |
| **Zen** | Test strategy, coverage analysis, failure investigation | Test generation, quality assessment, debugging |

**Workflow Integration:**
- **Test Planning**: Zen testgen → comprehensive test cases → Playwright implementation
- **Test Execution**: Playwright → browser automation → result collection
- **Failure Analysis**: Zen debug → root cause analysis → resolution planning
- **Quality Assessment**: Zen analyze → coverage metrics → improvement recommendations

## MCP Usage Protocols

### Research Workflows
1. **Broad Market Research**: Perplexity (primary) → Firecrawl (specific data) → Zen (validation)
2. **Technical Research**: Context7 (documentation) → Perplexity (trends) → Zen (analysis)
3. **Competitive Analysis**: Firecrawl (data extraction) → Perplexity (context) → Zen (synthesis)

### Development Workflows
1. **Pattern Research**: Context7 (existing patterns) → Zen analyze (duplication check)
2. **Implementation**: Context7 (API docs) → Zen debug (issue resolution)
3. **Quality Assurance**: Zen codereview (validation) → Zen precommit (consistency)

### Testing Workflows
1. **Test Design**: Zen testgen (strategy) → Playwright (implementation)
2. **Test Execution**: Playwright (automation) → Zen analyze (coverage)
3. **Issue Resolution**: Zen debug (analysis) → Playwright (validation)

### Architecture Workflows
1. **Technology Selection**: Context7 (research) → Zen thinkdeep (validation)
2. **Design Validation**: Zen analyze (impact) → Zen chat (brainstorming)
3. **Risk Assessment**: Multi-MCP research → Zen thinkdeep (comprehensive analysis)

## LEVER Framework Integration

### Leverage (L)
- **Context7**: Find existing libraries, patterns, and solutions
- **Perplexity**: Research proven approaches and industry standards
- **Zen analyze**: Identify reusable components in current codebase

### Extend (E)
- **Context7**: Research extension possibilities and compatibility
- **Zen thinkdeep**: Analyze extension architecture and design
- **Perplexity**: Find examples of successful extensions

### Verify (V)
- **Zen codereview**: Multi-model code validation
- **Playwright**: Automated testing and verification
- **Zen testgen**: Comprehensive test coverage validation

### Eliminate (E)
- **Zen analyze**: Detect code duplication and redundancy
- **Context7**: Research consolidation patterns
- **Zen debug**: Identify unnecessary complexity

### Reduce (R)
- **Zen chat**: Brainstorm simplification approaches
- **Context7**: Find simpler implementation patterns
- **Zen thinkdeep**: Analyze complexity reduction strategies

## Quality Gates and Validation

### Multi-MCP Validation Requirements
For critical decisions requiring high confidence:
1. **Research Validation**: Require agreement between Perplexity + Context7 + Zen
2. **Technical Decisions**: Require Context7 documentation + Zen multi-model consensus
3. **Architecture Decisions**: Require Context7 patterns + Zen thinkdeep validation
4. **Test Strategy**: Require Zen testgen + Playwright feasibility confirmation

### MCP Quality Standards
- **Source Attribution**: Always reference which MCP provided specific information
- **Cross-Validation**: Use multiple MCPs for critical findings validation
- **Current Information**: Prioritize Perplexity for real-time market conditions
- **Technical Accuracy**: Use Context7 for precise technical documentation

## Emergency Procedures

### When MCP Tools Conflict
1. **Immediate Assessment**: Identify specific areas of conflict between MCP recommendations
2. **Source Evaluation**: Assess recency and authority of conflicting information
3. **Multi-Model Validation**: Use Zen tools for objective conflict resolution
4. **User Escalation**: Report conflicts that cannot be resolved through validation

### When MCP Tools Are Unavailable
1. **Graceful Degradation**: Continue with available tools and note limitations
2. **Alternative Research**: Use traditional research methods where possible
3. **Quality Adjustment**: Adjust confidence levels and validation requirements
4. **Documentation**: Clearly note which MCPs were unavailable for transparency

## Best Practices

### For All Agents
- **Start Broad, Then Narrow**: Begin with comprehensive MCP research, then focus
- **Document Sources**: Always attribute findings to specific MCP tools
- **Validate Critical Decisions**: Use multiple MCPs for important recommendations
- **Stay Current**: Prefer real-time tools (Perplexity) for market conditions

### For Research-Heavy Tasks
- **Multi-Source Strategy**: Combine Perplexity + Firecrawl + Context7 for comprehensive coverage
- **Bias Detection**: Use different MCPs to identify potential analytical biases
- **Real-Time Validation**: Use Perplexity to confirm current relevance of findings

### For Technical Implementation
- **Pattern-First Approach**: Always check Context7 for existing patterns before creating new code
- **Continuous Validation**: Use Zen tools throughout development for quality assurance
- **Integration Testing**: Combine Context7 research with Playwright validation

## Success Metrics

### MCP Integration Effectiveness
- **Coverage**: Percentage of decisions using appropriate MCP validation
- **Quality**: Reduction in rework due to better initial research and validation
- **Speed**: Time savings from efficient tool usage and parallel research
- **Accuracy**: Improved decision quality through multi-source validation

### Agent Performance Enhancement
- **Research Quality**: Comprehensiveness and accuracy of research findings
- **Development Efficiency**: Faster implementation through better pattern research
- **Test Coverage**: More comprehensive testing through enhanced test generation
- **Architecture Quality**: Better decisions through multi-model validation

This matrix should be referenced by all PIB agents when planning their MCP tool usage for optimal effectiveness and quality outcomes.
