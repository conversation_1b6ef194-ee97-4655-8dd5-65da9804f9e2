# Firestore Security Rules Documentation

## Overview

This document describes the comprehensive security rules implemented for the PIB Calendar application's Firestore database. The rules replace the dangerous universal access pattern (`allow read, write: if true`) with proper granular permission-based security.

## Security Architecture

### Core Principles

1. **Authentication Required**: All operations require user authentication
2. **Principle of Least Privilege**: Users only get minimum necessary permissions
3. **Default Deny**: Explicit deny rule for any undefined collections
4. **Permission Hierarchy**: View < Edit < Admin permission levels
5. **Workspace Isolation**: Users can only access calendars within their workspaces
6. **Owner Controls**: Calendar owners have ultimate control over their calendars

### Permission System

The calendar sharing system implements three hierarchical permission levels:

```typescript
CalendarPermission {
  View = 'view',    // Level 1: Can view calendar events
  Edit = 'edit',    // Level 2: Can create/edit events + view
  Admin = 'admin'   // Level 3: Can manage calendar settings + edit + view
}
```

## Helper Functions

### Authentication Helpers

- `isAuthenticated()`: Verifies user has valid Firebase auth token
- `getCurrentUser()`: Returns current authenticated user ID
- `isOwnerOfDocument(userId)`: Checks if user owns a specific document

### Calendar Permission Helpers

- `isCalendarOwner(calendarId)`: Checks if user owns the calendar
- `isCalendarCollaborator(calendarId, requiredPermission)`: Checks collaborator status with permission level
- `isCalendarAdmin(calendarId)`: Checks if user has admin access (owner or admin collaborator)
- `canEditCalendar(calendarId)`: Checks if user can edit calendar (admin or edit permission)
- `canViewCalendar(calendarId)`: Checks if user can view calendar (any permission level)

### Workspace Helpers

- `isWorkspaceMember(workspaceId)`: Verifies workspace membership
- `hasPermissionLevel(userPermission, requiredPermission)`: Compares permission hierarchy

## Collection Rules

### 1. Calendars Collection (`/calendars/{calendarId}`)

**Purpose**: Core calendar documents containing calendar metadata and sharing settings.

**Rules**:
- **Read**: Owner or any collaborator with view/edit/admin permission
- **Create**: Authenticated users can create calendars they own within workspaces they belong to
- **Update**: Calendar admin (owner or admin collaborator), cannot change owner
- **Delete**: Calendar owner only

**Security Features**:
- Prevents ownership transfer
- Requires workspace membership for creation
- Enforces permission hierarchy

### 2. Calendar Collaborators Collection (`/calendar_collaborators/{collaboratorId}`)

**Purpose**: Manages shared access to calendars with permission levels.

**Document ID Format**: `{userId}_{calendarId}`

**Rules**:
- **Read**: Calendar admin, the collaborator themselves, or calendar owner
- **Create**: Calendar admin only, with strict validation
- **Update**: Calendar admin can modify permissions, core identifiers immutable
- **Delete**: Calendar admin or collaborator can remove themselves

**Security Features**:
- Enforces document ID format validation
- Requires workspace membership verification
- Prevents unauthorized collaborator addition
- Tracks who added each collaborator

### 3. Calendar Invitations Collection (`/calendar_invitations/{invitationId}`)

**Purpose**: Manages calendar sharing invitation workflow.

**Rules**:
- **Read**: Invitee, inviter, or calendar admin
- **Create**: Calendar admin only, must be the inviter
- **Update**: Invitee can respond, admin/inviter can modify status
- **Delete**: Invitee, inviter, or calendar admin

**Security Features**:
- Restricts invitation access to relevant parties
- Validates inviter is current user
- Allows controlled status updates
- Requires workspace membership

### 4. Calendar Activities Collection (`/calendar_activities/{activityId}`)

**Purpose**: Real-time presence tracking for collaborative editing.

**Document ID Format**: `{calendarId}_{userId}`

**Rules**:
- **Read**: Anyone with calendar view permission
- **Create/Update**: Users can manage their own activity only
- **Delete**: Activity owner or calendar admin

**Security Features**:
- Enforces activity ID format
- Requires calendar access for participation
- Prevents activity spoofing

### 5. Calendar Aggregation Views Collection (`/calendar_aggregation_views/{viewId}`)

**Purpose**: Custom calendar view configurations for multi-calendar displays.

**Rules**:
- **Read**: View owner or workspace members (for workspace views)
- **Create**: Users can create personal views, workspace views require membership
- **Update/Delete**: View owner only

**Security Features**:
- Supports both personal and workspace views
- Validates workspace membership for shared views
- Prevents unauthorized view modification

### 6. User and Workspace Collections

**Preserved existing rules for**:
- `users/{userId}`: User owns their own profile
- `workspaces/{workspaceId}`: Workspace members can read, owner can manage
- `workspace_members/{membershipId}`: Members can manage their membership
- `profiles/{profileId}`: Users can read all profiles, manage their own

## Security Validation

### Permission Checks

The rules implement multiple layers of validation:

1. **Authentication**: All operations require valid Firebase auth
2. **Authorization**: Permission-based access control
3. **Data Integrity**: Document format and content validation
4. **Relationship Validation**: Cross-collection consistency checks

### Example Permission Flow

```javascript
// User trying to read calendar events
1. isAuthenticated() ✓
2. canViewCalendar(calendarId)
   a. isCalendarOwner(calendarId) OR
   b. isCalendarCollaborator(calendarId, 'view') OR
   c. isCalendarCollaborator(calendarId, 'edit') OR
   d. isCalendarCollaborator(calendarId, 'admin')
3. Access granted if any condition true
```

### Invitation Security Flow

```javascript
// User accepting calendar invitation
1. isAuthenticated() ✓
2. getCurrentUser() == resource.data.invitedUserId ✓
3. request.resource.data.status in ['accepted', 'declined'] ✓
4. resource.data.invitedUserId == request.resource.data.invitedUserId ✓
5. Update allowed
```

## Security Improvements

### Fixed Vulnerabilities

1. **Universal Access**: Removed `allow read, write: if true`
2. **Unauthorized Calendar Access**: Implemented permission-based access
3. **Cross-Workspace Data Leakage**: Added workspace boundary enforcement
4. **Permission Escalation**: Implemented hierarchical permission validation
5. **Data Tampering**: Added document integrity checks

### Defense in Depth

1. **Authentication Layer**: Firebase Auth token validation
2. **Authorization Layer**: Custom permission functions
3. **Data Validation Layer**: Document structure and content rules
4. **Audit Layer**: Built-in Firestore security rule logging

## Testing and Validation

### Rule Testing Commands

```bash
# Start Firebase emulator with new rules
firebase emulators:start --only firestore

# Test rules with Firebase emulator
firebase emulators:exec "npm test" --only firestore

# Deploy rules to production
pnpm deploy:rules
```

### Test Scenarios

1. **Unauthorized Access**: Verify non-members cannot access calendars
2. **Permission Escalation**: Confirm users cannot grant themselves higher permissions
3. **Cross-Workspace Access**: Ensure workspace isolation
4. **Owner Controls**: Validate owner-only operations
5. **Collaborator Management**: Test invitation and permission workflows

## Migration Considerations

### Breaking Changes

1. **Development Access**: No longer allows universal read/write
2. **Testing**: Requires proper authentication in tests
3. **Admin Tools**: Must use service accounts or admin SDK

### Deployment Steps

1. **Test Rules**: Thoroughly test with emulator
2. **Backup Data**: Export current Firestore data
3. **Deploy Rules**: Use `pnpm deploy:rules`
4. **Monitor**: Watch for permission denied errors
5. **Rollback Plan**: Keep previous rules as backup

## Monitoring and Maintenance

### Security Monitoring

1. **Permission Denied Logs**: Monitor for legitimate access issues
2. **Usage Patterns**: Track calendar sharing adoption
3. **Performance Impact**: Monitor rule evaluation performance
4. **Error Rates**: Watch for increased client errors

### Maintenance Tasks

1. **Regular Audits**: Review rules quarterly
2. **Performance Optimization**: Optimize complex permission checks
3. **Feature Updates**: Update rules for new sharing features
4. **Security Reviews**: Annual security assessment

## Best Practices

### Development

1. **Test with Emulator**: Always test rule changes locally
2. **Gradual Rollout**: Deploy rules incrementally
3. **Error Handling**: Implement proper client-side error handling
4. **Documentation**: Keep rules documentation current

### Security

1. **Principle of Least Privilege**: Only grant minimum necessary permissions
2. **Defense in Depth**: Layer security controls
3. **Regular Reviews**: Audit rules and permissions regularly
4. **Incident Response**: Have rollback procedures ready

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check user authentication and workspace membership
2. **Document Format Errors**: Verify document ID formats match expectations
3. **Cross-Reference Failures**: Ensure referenced documents exist
4. **Performance Issues**: Review complex rule evaluations

### Debug Steps

1. **Enable Rule Debugging**: Use Firebase emulator debug mode
2. **Check Authentication**: Verify user is properly authenticated
3. **Validate Permissions**: Check user's calendar permissions
4. **Review Logs**: Examine Firestore security rule logs
5. **Test Incrementally**: Isolate problematic operations

## Future Enhancements

### Planned Security Features

1. **Rate Limiting**: Implement operation rate limits
2. **Advanced Auditing**: Enhanced security logging
3. **External Sharing**: Secure external calendar sharing
4. **API Keys**: Service account integration
5. **Compliance**: GDPR and privacy regulation compliance

### Performance Optimizations

1. **Rule Caching**: Cache permission lookups
2. **Batch Operations**: Optimize multi-document operations
3. **Index Optimization**: Improve query performance
4. **Rule Simplification**: Streamline complex permission checks
