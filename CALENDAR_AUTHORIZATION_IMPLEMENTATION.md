# Calendar Authorization Implementation

## Overview

This document outlines the implementation of authorization checks in the calendar sharing functions to fix critical security vulnerabilities. The implementation adds comprehensive permission validation to ensure only authorized users can perform sensitive calendar operations.

## Security Vulnerabilities Fixed

### 1. Missing Permission Validation
**Issue**: Functions like `updateCollaboratorPermission()`, `removeCollaborator()`, `shareCalendar()`, and `inviteCollaborators()` lacked permission validation.
**Fix**: Added authorization checks before all write operations.

### 2. Insufficient Access Control
**Issue**: Any user could potentially modify calendar sharing settings and collaborator permissions.
**Fix**: Implemented role-based access control with proper permission verification.

## Implementation Details

### Authorization Helper Functions

#### `verifyAdminPermission(calendarId: string, userId: string): Promise<boolean>`
- Checks if user is calendar owner OR has admin permission as collaborator
- Returns `true` if user has admin rights, `false` otherwise
- Used for operations requiring administrative privileges

#### `verifyOwnership(calendarId: string, userId: string): Promise<boolean>`
- Checks if user owns the calendar by comparing userId field
- Returns `true` if user is the calendar owner
- Used for operations that only owners should perform

#### `getUserCalendarPermission(calendarId: string, userId: string): Promise<CalendarPermission | null>`
- Returns user's permission level for a calendar
- Checks ownership first (owners get admin rights)
- Falls back to collaborator permission lookup
- Returns `null` if user has no access

### Authorization Model

```
Owner (calendar.userId)
├── Full admin rights
├── Can share/unshare calendar
├── Can manage all collaborators
└── Can delete calendar

Admin Permission
├── Can manage sharing settings
├── Can invite/remove collaborators
├── Can modify collaborator permissions
└── Cannot delete calendar

Edit Permission
├── Can create/modify events
├── Cannot manage sharing
└── Cannot manage collaborators

View Permission
├── Can view calendar events
├── Cannot modify anything
└── Read-only access
```

### Protected Functions

#### `shareCalendar(request: ShareCalendarRequest)`
- **Authorization**: Requires admin permission
- **Validates**: User can manage sharing settings
- **Error**: `CalendarAuthorizationError` if insufficient permission

#### `inviteCollaborators(calendarId: string, inviteData: {...})`
- **Authorization**: Requires admin permission
- **Validates**: User can send invitations
- **Error**: `CalendarAuthorizationError` if insufficient permission

#### `updateCollaboratorPermission(collaboratorId: string, newPermission: CalendarPermission)`
- **Authorization**: Requires admin permission on the calendar
- **Validates**: User can modify collaborator permissions
- **Additional**: Fetches calendar ID from collaborator record
- **Error**: `CalendarAuthorizationError` if insufficient permission

#### `removeCollaborator(collaboratorId: string)`
- **Authorization**: Requires admin permission on the calendar
- **Validates**: User can remove collaborators
- **Additional**: Fetches calendar ID from collaborator record
- **Error**: `CalendarAuthorizationError` if insufficient permission

#### `getCalendarCollaborators(calendarId: string)`
- **Authorization**: Requires at least view permission
- **Validates**: User has access to the calendar
- **Error**: `CalendarAuthorizationError` if no access

### Error Handling

#### Custom Error Class
```typescript
class CalendarAuthorizationError extends Error {
  constructor(message: string, public readonly code: string = 'UNAUTHORIZED') {
    super(message)
    this.name = 'CalendarAuthorizationError'
  }
}
```

#### Error Codes
- `NO_CONTEXT`: User not authenticated or missing workspace context
- `INSUFFICIENT_PERMISSION`: User lacks required permission level
- `NOT_FOUND`: Referenced calendar or collaborator not found
- `UNAUTHORIZED`: Generic authorization failure

#### User-Friendly Error Messages
- Authorization errors show specific permission requirements
- Different toast notifications for permission vs. system errors
- Uses shield icon for permission-related errors

### Integration with Auth System

#### Current Profile Integration
```typescript
const { currentProfile } = useAuth()

// All functions validate currentProfile.value exists
if (!currentProfile.value?.userId) {
  throw new CalendarAuthorizationError('User not authenticated', 'NO_CONTEXT')
}
```

#### Workspace Context
- All operations require valid workspace context
- Validates `currentProfile.value.workspaceId` exists
- Ensures operations are scoped to current workspace

### API Exposure

#### Public Helper Methods
The composable exposes authorization helpers for external use:

```typescript
const {
  verifyAdminPermission,
  verifyOwnership,
  getUserCalendarPermission
} = useCalendarSharing()

// Check permissions before showing UI elements
const canManageSharing = await verifyAdminPermission('cal_123')
if (canManageSharing) {
  // Show sharing management UI
}
```

### Security Best Practices

#### Defense in Depth
1. **Frontend Validation**: Check permissions before API calls
2. **Backend Validation**: Server-side rules enforce permissions
3. **UI Controls**: Hide unauthorized actions from users

#### Principle of Least Privilege
- Users only get minimum required permissions
- Operations fail safely with clear error messages
- No silent failures or data leaks

#### Audit Trail
- All authorization checks are logged
- Failed permission checks are tracked
- User actions include proper context

## Testing Recommendations

### Unit Tests
- Test each authorization helper function
- Verify correct permission calculations
- Test error cases and edge conditions

### Integration Tests
- Test full workflow with different permission levels
- Verify error handling and user feedback
- Test workspace switching scenarios

### Security Tests
- Attempt unauthorized operations
- Verify permission boundaries
- Test with invalid/missing user context

## Future Enhancements

### Potential Improvements
1. **Permission Caching**: Cache permission lookups for performance
2. **Audit Logging**: Track all authorization decisions
3. **Permission Templates**: Predefined permission sets for common scenarios
4. **Delegation**: Allow temporary permission elevation

### Migration Notes
- Existing installations may need permission data migration
- Consider adding default permissions for existing collaborators
- Implement graceful degradation for legacy data

## Conclusion

This implementation provides comprehensive authorization for calendar sharing operations, addressing critical security vulnerabilities while maintaining usability. The system follows security best practices and integrates seamlessly with the existing authentication system.
