import type { InjectionKey } from 'vue'
import { LayoutCollapseContextSymbol } from './layout-collapse'

// Helper function for optional injections without warnings
function tryInject<T>(key: InjectionKey<T> | string | symbol): T | null {
  try {
    // Temporarily suppress console warnings
    const originalWarn = console.warn
    console.warn = () => {}
    const result = inject(key, null) as T | null
    console.warn = originalWarn
    return result
  } catch {
    return null
  }
}

interface LayoutSidebarContext {
  isMobileOpen: Ref<boolean>
  isCollapsed: Ref<boolean>
  currentSubsidebarId: Ref<string | undefined>
  currentSubsidebarWidth: ComputedRef<string>
  toggleMobileNav: () => void
}

const LayoutSidebarContextSymbol = Symbol('LayoutSidebarContext') as InjectionKey<LayoutSidebarContext>

export function createLayoutSidebarContext({
  defaultSubsidebarId = '',
  modelValue,
}: {
  defaultSubsidebarId?: string
  modelValue?: Ref<string | undefined>
} = {}): LayoutSidebarContext {
  // Try to get collapse context if available, fallback to default values
  const collapseContext = tryInject(LayoutCollapseContextSymbol)

  const { xl } = useTailwindBreakpoints()
  const isMobileOpen = ref(false)
  const isCollapsed = collapseContext?.isCollapsed ?? ref(false)
  const currentSubsidebarId = modelValue !== undefined ? modelValue : ref(defaultSubsidebarId)

  watch(xl, (value) => {
    if (value) {
      isMobileOpen.value = false
    }
  })

  const currentSubsidebarWidth = computed(() => {
    return (isMobileOpen.value || !isCollapsed.value) && currentSubsidebarId.value
      ? 'var(--tairo-sidebar-subsidebar-width)'
      : '0px'
  })

  const context = {
    isMobileOpen,
    isCollapsed,
    currentSubsidebarId,
    currentSubsidebarWidth,
    toggleMobileNav() {
      isMobileOpen.value = !isMobileOpen.value
    },
  }
  provide(LayoutSidebarContextSymbol, context)
  return context
}

export function useLayoutSidebarContext() {
  const context = inject(LayoutSidebarContextSymbol)
  if (!context) {
    throw new Error('useLayoutSidebarContext: Should be used inside <TairoSidebarLayout> component')
  }
  return context
}
