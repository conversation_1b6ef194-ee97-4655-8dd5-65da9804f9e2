<script lang="ts">
export interface TairoModalProps {
  modelValue?: boolean
  open?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  title?: string
}
</script>

<script setup lang="ts">
defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(defineProps<TairoModalProps>(), {
  modelValue: undefined,
  open: false,
  size: 'md',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

// Use a ref to ensure consistent state between server and client
const isOpen = ref(false)

// Computed to determine which prop to use (v-model takes precedence)
const isModalOpen = computed(() => props.modelValue !== undefined ? props.modelValue : props.open)

// Update isOpen when the modal state changes, but only on client side
onMounted(() => {
  isOpen.value = isModalOpen.value
})

watchEffect(() => {
  if (process.client) {
    isOpen.value = isModalOpen.value
  }
})

watch(isOpen, (value) => {
  if (!value) {
    emit('update:modelValue', false)
    emit('close')
  }
})

function closeModal() {
  emit('update:modelValue', false)
  emit('close')
}

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'xs':
      return 'max-w-xs'
    case 'sm':
      return 'max-w-sm'
    case 'md':
      return 'max-w-md'
    case 'lg':
      return 'max-w-lg'
    case 'xl':
      return 'max-w-xl'
    case 'full':
      return 'max-w-full'
    default:
      return 'max-w-md'
  }
})
</script>

<template>
  <ClientOnly>
    <template #default>
      <DialogRoot v-model:open="isOpen">
        <DialogPortal>
          <DialogOverlay class="bg-muted-800/70 dark:bg-muted-900/80 fixed inset-0 z-50" />

          <DialogContent
            class="p-0 fixed starting:opacity-0 starting:top-[8%] top-[10%] start-[50%] max-h-[85vh] w-[90vw] translate-x-[-50%] text-sm rounded-lg overflow-hidden border border-white dark:border-muted-700 bg-white dark:bg-muted-800 focus:outline-none z-[100] transition-discrete transition-all duration-200 ease-out flex flex-col" :class="[
              sizeClasses,
            ]"
          >
            <!-- Header -->
            <div v-if="$slots.header || props.title" class="flex w-full items-center justify-between p-4 md:p-6 border-b border-muted-200 dark:border-muted-700">
              <DialogTitle v-if="$slots.header" as-child>
                <slot name="header" />
              </DialogTitle>
              <DialogTitle v-else-if="props.title" class="text-lg font-medium text-muted-900 dark:text-muted-100">
                {{ props.title }}
              </DialogTitle>

              <DialogClose as-child>
                <BaseButton size="icon" variant="ghost" @click="closeModal">
                  <Icon name="lucide:x" class="size-4" />
                </BaseButton>
              </DialogClose>
            </div>

            <!-- Body -->
            <div class="flex-1 overflow-y-auto p-4 md:p-6">
              <slot />
            </div>

            <!-- Footer -->
            <div v-if="$slots.footer" class="flex w-full items-center justify-end gap-3 p-4 md:p-6 border-t border-muted-200 dark:border-muted-700">
              <slot name="footer" />
            </div>
          </DialogContent>
        </DialogPortal>
      </DialogRoot>
    </template>
    <template #fallback>
      <!-- Provide a placeholder during SSR to prevent layout shift -->
      <div v-if="isModalOpen" class="hidden" />
    </template>
  </ClientOnly>
</template>
