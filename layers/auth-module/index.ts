/**
 * Auth Module Entry Point
 * Main integration point for the authentication module
 */

import { addImportsDir, addPlugin, createResolver, defineNuxtModule } from '@nuxt/kit'

// Module options interface
export interface AuthModuleOptions {
  firebase?: {
    enabled?: boolean
    config?: any
  }
  persistence?: {
    enabled?: boolean
    ttl?: number
  }
  performance?: {
    batchingEnabled?: boolean
    cacheEnabled?: boolean
    cacheTTL?: number
  }
  errorHandling?: {
    logErrors?: boolean
    reportErrors?: boolean
  }
}

export default defineNuxtModule<AuthModuleOptions>({
  meta: {
    name: '@pib/auth-module',
    configKey: 'authModule',
    compatibility: {
      nuxt: '^3.0.0',
    },
  },

  defaults: {
    firebase: {
      enabled: true,
    },
    persistence: {
      enabled: true,
      ttl: 24 * 60 * 60 * 1000, // 24 hours
    },
    performance: {
      batchingEnabled: true,
      cacheEnabled: true,
      cacheTTL: 5 * 60 * 1000, // 5 minutes
    },
    errorHandling: {
      logErrors: true,
      reportErrors: false,
    },
  },

  async setup(options, nuxt) {
    const resolver = createResolver(import.meta.url)

    // Add runtime config
    nuxt.options.runtimeConfig.public.authModule = options

    // Add composables
    addImportsDir(resolver.resolve('./composables'))

    // Add utils as auto-imports
    nuxt.options.imports = nuxt.options.imports || { dirs: [] }
    if (Array.isArray(nuxt.options.imports.dirs)) {
      nuxt.options.imports.dirs.push(resolver.resolve('./utils'))
    }

    // Add components
    nuxt.hook('components:dirs', (dirs) => {
      dirs.push({
        path: resolver.resolve('./components'),
        prefix: 'Auth',
      })
    })

    // Add types
    nuxt.hook('prepare:types', ({ references }) => {
      references.push({
        path: resolver.resolve('./types/index.d.ts'),
      })
    })

    // Add plugins
    addPlugin({
      src: resolver.resolve('./plugins/auth-module.client.ts'),
      mode: 'client',
    })

    // Add Firebase plugin if enabled
    if (options.firebase?.enabled) {
      addPlugin({
        src: resolver.resolve('./plugins/firebase.client.ts'),
        mode: 'client',
      })
    }

    // Add middleware
    nuxt.options.dir = nuxt.options.dir || {}
    nuxt.options.dir.middleware = nuxt.options.dir.middleware || resolver.resolve('./middleware')

    // Add layouts
    nuxt.options.dir.layouts = nuxt.options.dir.layouts || resolver.resolve('./layouts')

    // Add pages - commented out to avoid overriding app pages
    // nuxt.options.dir.pages = nuxt.options.dir.pages || resolver.resolve('./pages')

    // Module-specific hooks
    nuxt.hook('app:mounted', () => {
      console.info('[Auth Module] Application mounted with auth module')
    })

    // Development helpers
    if (nuxt.options.dev) {
      nuxt.hook('devtools:customTabs', (tabs) => {
        tabs.push({
          name: 'auth-module',
          title: 'Auth Module',
          icon: 'i-mdi-account-key',
          view: {
            type: 'iframe',
            src: '/__auth-module-devtools',
          },
        })
      })
    }

    console.info('[Auth Module] Module setup completed')
  },
})

// Re-export all composables and types from composables/index.ts
export * from './composables'

export { authErrorHandler, useAuthErrorHandler } from './utils/error-handler'
export type { ErrorContext, ErrorReport } from './utils/error-handler'
// Export utility functions
export { authEventBus, useAuthEventBus } from './utils/event-bus'
// Export types
export type { AuthEventPayload, AuthModuleEvents } from './utils/event-bus'

export {
  BatchProcessor,
  debounce,
  MemoryCache,
  performanceMonitor,
  RequestDeduplicator,
  throttle,
  usePerformanceUtils,
} from './utils/performance'
export { statePersistence, useStatePersistence } from './utils/state-persistence'
export type { PersistedAuthState } from './utils/state-persistence'
