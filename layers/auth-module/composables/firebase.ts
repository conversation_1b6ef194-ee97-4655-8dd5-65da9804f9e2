import { initializeApp } from 'firebase/app'
import {
  browserSessionPersistence,
  connectAuthEmulator,
  getAuth,
  GoogleAuthProvider,
  onAuthStateChanged,
  setPersistence,
} from 'firebase/auth'
import { connectFirestoreEmulator, getFirestore } from 'firebase/firestore'
import { getMessaging } from 'firebase/messaging'
import { connectStorageEmulator, getStorage } from 'firebase/storage'
import { getGenerativeModel, getVertexAI } from 'firebase/vertexai'

// Retry logic for Firestore operations to handle WebChannelConnection errors
export async function retryFirestoreOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
): Promise<T> {
  let lastError: Error | undefined

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    }
    catch (error: any) {
      lastError = error
      console.warn(`Firestore operation attempt ${attempt} failed:`, error.message)

      // Don't retry on certain error types
      if (
        error.code === 'permission-denied'
        || error.code === 'unauthenticated'
        || error.code === 'invalid-argument'
      ) {
        throw error
      }

      if (attempt < maxRetries) {
        console.warn(`Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
  }

  throw new Error(lastError?.message || 'Firestore operation failed after retries')
}

// Singleton instances to prevent multiple initializations
let firebaseAppInstance: any = null
let isEmulatorsConnected = false
let authInstance: any = null
let firestoreInstance: any = null
let storageInstance: any = null
let messagingInstance: any = null
let isInitialized = false
let googleProviderInstance: any = null
let vertexAIInstance: any = null
let modelInstance: any = null

// Initialize Firebase and connect to emulators only once
function initializeFirebaseOnce() {
  if (isInitialized) return

  const config = useRuntimeConfig?.() || { public: { firebase: { useEmulator: false } } }
  const useEmulator = config.public.firebase?.useEmulator === true || config.public.firebase?.useEmulator === 'true'
  const firebaseConfig: any = config.public.firebase

  // Initialize Firebase app
  firebaseAppInstance = initializeApp(firebaseConfig)
  authInstance = getAuth(firebaseAppInstance)
  firestoreInstance = getFirestore(firebaseAppInstance)
  
  // Initialize Google provider
  googleProviderInstance = new GoogleAuthProvider()
  googleProviderInstance.addScope('profile')
  googleProviderInstance.addScope('email')
  
  // Initialize Vertex AI
  vertexAIInstance = getVertexAI(firebaseAppInstance)
  modelInstance = getGenerativeModel(vertexAIInstance, {
    model: 'gemini-1.5-flash-preview-0514',
  })

  // Client-side only initializations
  if (typeof window !== 'undefined') {
    // Initialize messaging and storage
    messagingInstance = getMessaging(firebaseAppInstance)
    storageInstance = getStorage(firebaseAppInstance)

    // Connect to emulators if enabled
    if (useEmulator && !isEmulatorsConnected) {
      const host = (firestoreInstance.toJSON() as { settings?: { host?: string } }).settings?.host ?? ''
      
      // Connect Storage emulator
      try {
        connectStorageEmulator(storageInstance, 'localhost', 9199)
        console.warn('[Firebase] Connected to Storage emulator')
      } catch (error) {
        console.warn('[Firebase] Storage emulator connection failed:', error)
      }

      // Connect Firestore emulator
      if (!host.startsWith('localhost')) {
        try {
          connectFirestoreEmulator(firestoreInstance, 'localhost', 8080)
          console.warn('[Firebase] Connected to Firestore emulator')
        } catch (error) {
          console.warn('[Firebase] Firestore emulator connection failed:', error)
        }
      }

      // Connect Auth emulator
      const authUrl = host.startsWith('localhost') ? 'http://localhost:9099' : 'http://127.0.0.1:9099'
      try {
        connectAuthEmulator(authInstance, authUrl, { disableWarnings: true })
        console.warn('[Firebase] Connected to Auth emulator')
      } catch (error) {
        console.warn('[Firebase] Auth emulator connection failed:', error)
      }

      isEmulatorsConnected = true
    }

    // Set persistence
    if (!useEmulator) {
      setPersistence(authInstance, browserSessionPersistence).catch((err) => {
        console.error('[Firebase] Error enabling auth persistence:', err)
      })
    }

    // Network state monitoring to help diagnose connection issues
    const handleOnline = () => {
      console.warn('[Firebase] Network connection restored - Firestore should reconnect')
    }

    const handleOffline = () => {
      console.warn('[Firebase] Network connection lost - this may cause WebChannelConnection errors')
    }

    globalThis.addEventListener('online', handleOnline)
    globalThis.addEventListener('offline', handleOffline)

    // Log initialization complete
    console.warn('[Firebase] Initialization complete')
  }

  isInitialized = true
}

export function useFirebase() {
  // Initialize once on first call
  initializeFirebaseOnce()

  const firebaseApp = firebaseAppInstance
  const auth = authInstance
  const firestore = firestoreInstance
  const googleProvider = googleProviderInstance
  const storage = storageInstance
  const messaging = messagingInstance
  const vertexAI = vertexAIInstance
  const model = modelInstance

  // Add Firestore connection monitoring to help debug WebChannelConnection issues
  const monitorFirestoreConnection = () => {
    if (typeof window !== 'undefined') {
      // Monitor for network errors that could cause WebChannelConnection issues
      const handleError = (event: ErrorEvent) => {
        if (event.message?.includes('WebChannelConnection') || event.message?.includes('RPC')) {
          console.error('[Firebase] Detected WebChannelConnection error:', event.message)
          console.warn('[Firebase] Consider checking your network connection or Firebase emulator status')
        }
      }

      window.addEventListener('error', handleError)

      // Return cleanup function
      return () => {
        window.removeEventListener('error', handleError)
      }
    }
    return () => {}
  }

  return {
    firebaseApp,
    firestore,
    auth,
    googleProvider,
    vertexAI,
    model,
    storage,
    onAuthStateChanged,
    messaging,
    monitorFirestoreConnection,
    retryFirestoreOperation,
  }
}
