import type { Ref } from 'vue'

interface SecurityConfig {
  enableClientValidation: boolean
  maxMessageLength: number
  enableXSSProtection: boolean
  rateLimitWarning: boolean
}

interface SecurityEvent {
  type: 'validation' | 'sanitization' | 'rate_limit' | 'xss_attempt'
  severity: 'low' | 'medium' | 'high'
  message: string
  timestamp: Date
}

const defaultConfig: SecurityConfig = {
  enableClientValidation: true,
  maxMessageLength: 50000,
  enableXSSProtection: true,
  rateLimitWarning: true,
}

// Simple client-side XSS detection patterns
const XSS_PATTERNS = [
  /<script[^>]*>.*?<\/script>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /on\w+\s*=/gi,
  /<iframe[^>]*>/gi,
  /<object[^>]*>/gi,
  /<embed[^>]*>/gi,
]

// Client-side rate limiting tracking
const requestCounts = new Map<string, { count: number, resetTime: number }>()

export function useSecurity(config: Partial<SecurityConfig> = {}) {
  const securityConfig = { ...defaultConfig, ...config }
  const securityEvents: Ref<SecurityEvent[]> = ref([])
  const isRateLimited: Ref<boolean> = ref(false)

  // Return a minimal implementation during SSR
  if (!process.client) {
    return {
      securityEvents: readonly(securityEvents),
      isRateLimited: readonly(isRateLimited),
      validateMessage: () => ({ isValid: true, issues: [] }),
      sanitizeMessage: (message: string) => message,
      checkRateLimit: () => true,
      validateFileUpload: () => ({ isValid: true, issues: [] }),
      validateUrl: () => ({ isValid: true, issues: [] }),
      getCsrfToken: () => '',
      logSecurityEvent: () => {},
      clearSecurityEvents: () => {},
      getSecurityEvents: () => [],
      config: securityConfig,
    }
  }

  // Log security event
  const logSecurityEvent = (event: Omit<SecurityEvent, 'timestamp'>) => {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    }

    securityEvents.value.push(securityEvent)

    // Keep only last 100 events
    if (securityEvents.value.length > 100) {
      securityEvents.value = securityEvents.value.slice(-100)
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      // Only log actual security issues, not info messages
      if (securityEvent.severity !== 'low') {
        console.warn('[CLIENT SECURITY]', securityEvent)
      }
    }
  }

  // Client-side message validation
  const validateMessage = (message: string): { isValid: boolean, issues: string[] } => {
    const issues: string[] = []

    if (!securityConfig.enableClientValidation) {
      return { isValid: true, issues }
    }

    // Length validation
    if (message.length > securityConfig.maxMessageLength) {
      issues.push(`Message exceeds maximum length of ${securityConfig.maxMessageLength} characters`)
    }

    // XSS detection
    if (securityConfig.enableXSSProtection) {
      for (const pattern of XSS_PATTERNS) {
        if (pattern.test(message)) {
          issues.push('Potentially harmful content detected')
          logSecurityEvent({
            type: 'xss_attempt',
            severity: 'high',
            message: 'XSS pattern detected in user input',
          })
          break
        }
      }
    }

    // Basic injection detection - more specific patterns to avoid false positives
    const injectionPatterns = [
      /(\bunion\s+select\b)/gi, // Only match actual SQL union select patterns
      /(\bselect(?:\s+\S.*(?:[\n\r\u2028\u2029]\s*|[\t\v\f \xA0\u1680\u2000-\u200A\u202F\u205F\u3000\uFEFF])|\s{2,})from\s+)/gi, // Actual SQL select statements
      /(\binsert\s+into\s+)/gi, // SQL insert statements
      /(\bdelete\s+from\s+)/gi, // SQL delete statements
      /(\bupdate(?:\s+\S.*(?:[\n\r\u2028\u2029]\s*|[\t\v\f \xA0\u1680\u2000-\u200A\u202F\u205F\u3000\uFEFF])|\s{2,})set\s+)/gi, // SQL update statements
      /(\bdrop\s+table\s+)/gi, // SQL drop table statements
      /(\b(or|and)\s+\d+\s*=\s*\d+)/gi, // SQL boolean injections
      /(;(\s)*(drop|delete|insert|update|create|alter))/gi, // SQL commands after semicolon
    ]

    for (const pattern of injectionPatterns) {
      if (pattern.test(message)) {
        issues.push('Potentially malicious content detected')
        logSecurityEvent({
          type: 'validation',
          severity: 'medium',
          message: 'Injection pattern detected in user input',
        })
        break
      }
    }

    if (issues.length > 0) {
      logSecurityEvent({
        type: 'validation',
        severity: 'medium',
        message: `Message validation failed: ${issues.join(', ')}`,
      })
    }

    return {
      isValid: issues.length === 0,
      issues,
    }
  }

  // Simple client-side sanitization
  const sanitizeMessage = (message: string): string => {
    if (!securityConfig.enableXSSProtection) {
      return message
    }

    let sanitized = message
    let wasModified = false

    // Remove script tags
    const originalLength = sanitized.length
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '[SCRIPT_REMOVED]')

    if (sanitized.length !== originalLength) {
      wasModified = true
    }

    // Remove javascript: protocols
    sanitized = sanitized.replace(/javascript:/gi, 'javascript-removed:')

    // Remove event handlers
    sanitized = sanitized.replace(/on\w+\s*=/gi, 'on-event-removed=')

    if (wasModified) {
      logSecurityEvent({
        type: 'sanitization',
        severity: 'medium',
        message: 'Message content was sanitized',
      })
    }

    return sanitized
  }

  // Client-side rate limiting check
  const checkRateLimit = (key: string, limit: number, windowMs: number): boolean => {
    if (!securityConfig.rateLimitWarning) {
      return true
    }

    const now = Date.now()
    const userRequests = requestCounts.get(key)

    if (!userRequests || now > userRequests.resetTime) {
      requestCounts.set(key, { count: 1, resetTime: now + windowMs })
      isRateLimited.value = false
      return true
    }

    if (userRequests.count >= limit) {
      isRateLimited.value = true
      logSecurityEvent({
        type: 'rate_limit',
        severity: 'medium',
        message: 'Rate limit exceeded',
      })
      return false
    }

    userRequests.count++
    isRateLimited.value = false
    return true
  }

  // Validate file upload
  const validateFileUpload = (file: File): { isValid: boolean, issues: string[] } => {
    const issues: string[] = []

    // File size validation (10MB limit)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      issues.push(`File size exceeds ${maxSize / 1024 / 1024}MB limit`)
    }

    // File type validation
    const allowedTypes = [
      'text/plain',
      'text/markdown',
      'application/json',
      'text/csv',
      'application/pdf',
      'image/png',
      'image/jpeg',
      'image/gif',
      'image/webp',
    ]

    if (!allowedTypes.includes(file.type)) {
      issues.push(`File type ${file.type} is not allowed`)
    }

    // Filename validation
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.sh', '.ps1', '.vbs', '.jar', '.scr', '.com', '.pif']
    const extension = `.${file.name.split('.').pop()?.toLowerCase()}`

    if (dangerousExtensions.includes(extension)) {
      issues.push(`File extension ${extension} is not allowed`)
    }

    // Check for path traversal in filename
    if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
      issues.push('Invalid characters in filename')
    }

    if (issues.length > 0) {
      logSecurityEvent({
        type: 'validation',
        severity: 'medium',
        message: `File validation failed: ${issues.join(', ')}`,
      })
    }

    return {
      isValid: issues.length === 0,
      issues,
    }
  }

  // Validate URL input
  const validateUrl = (url: string): { isValid: boolean, issues: string[] } => {
    const issues: string[] = []

    try {
      const urlObj = new URL(url)

      // Protocol validation
      const allowedProtocols = ['http:', 'https:']
      if (!allowedProtocols.includes(urlObj.protocol)) {
        issues.push(`Protocol ${urlObj.protocol} is not allowed`)
      }

      // Localhost validation in production
      if (process.env.NODE_ENV === 'production') {
        const hostname = urlObj.hostname.toLowerCase()
        if (hostname === 'localhost' || hostname.startsWith('127.') || hostname.startsWith('192.168.')) {
          issues.push('Local URLs are not allowed in production')
        }
      }
    }
    catch (error) {
      issues.push('Invalid URL format')
    }

    if (issues.length > 0) {
      logSecurityEvent({
        type: 'validation',
        severity: 'medium',
        message: `URL validation failed: ${issues.join(', ')}`,
      })
    }

    return {
      isValid: issues.length === 0,
      issues,
    }
  }

  // CSRF token management
  const getCsrfToken = (): string => {
    if (!process.client || typeof document === 'undefined') {
      return ''
    }

    // Get CSRF token from meta tag or cookie
    const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (metaToken)
      return metaToken

    // Fallback to cookie
    const cookies = document.cookie.split(';')
    const csrfCookie = cookies.find(cookie => cookie.trim().startsWith('csrf-token='))
    if (csrfCookie) {
      return csrfCookie.split('=')[1]
    }

    // Generate a temporary token if none exists
    return typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : ''
  }

  // Clear security events
  const clearSecurityEvents = () => {
    securityEvents.value = []
  }

  // Get security events by type
  const getSecurityEvents = (type?: SecurityEvent['type']) => {
    if (!type)
      return securityEvents.value
    return securityEvents.value.filter(event => event.type === type)
  }

  // Cleanup function for rate limit tracking
  const cleanup = () => {
    const now = Date.now()
    for (const [key, data] of requestCounts.entries()) {
      if (now > data.resetTime) {
        requestCounts.delete(key)
      }
    }
  }

  // Setup cleanup interval
  if (process.client) {
    setInterval(cleanup, 60000) // Cleanup every minute
  }

  return {
    // State
    securityEvents: readonly(securityEvents),
    isRateLimited: readonly(isRateLimited),

    // Methods
    validateMessage,
    sanitizeMessage,
    checkRateLimit,
    validateFileUpload,
    validateUrl,
    getCsrfToken,
    logSecurityEvent,
    clearSecurityEvents,
    getSecurityEvents,

    // Config
    config: securityConfig,
  }
}
