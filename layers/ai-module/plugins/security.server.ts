// Server-side security plugin for AI module
import { securityConfiguration } from '../server/config/security'
import { securityLogger } from '../utils/security'

export default defineNuxtPlugin(async () => {
  // Initialize server-side security logging
  if (process.server) {
    // Silent initialization - these are informational logs only
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG === 'true') {
      console.log('[AI Security] Initializing security middleware...')
      
      // Log security configuration on startup
      securityLogger.log({
        type: 'input_sanitized',
        severity: 'low',
        source: 'security_initialization',
        details: {
          maxMessageLength: securityConfiguration.security.maxMessageLength,
          contentFilteringEnabled: securityConfiguration.security.enableContentFiltering,
          xssProtectionEnabled: securityConfiguration.security.enableXSSProtection,
          rateLimitingEnabled: true,
        },
      })

      console.log('[AI Security] Security middleware initialized successfully')
    }
  }
})
