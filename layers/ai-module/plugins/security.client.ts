// Client-side security plugin for AI module
export default defineNuxtPlugin(() => {
  // Only run on client side to avoid hydration mismatches
  if (!process.client) {
    return {
      provide: {
        security: null,
      },
    }
  }

  // Initialize client-side security
  const security = useSecurity({
    enableClientValidation: true,
    maxMessageLength: 50000,
    enableXSSProtection: true,
    rateLimitWarning: true,
  })

  // Add global error handler for security validation errors
  const originalConsoleError = console.error
  console.error = (...args) => {
    const error = args[0]
    if (error instanceof Error && error.message.includes('validation')) {
      security.logSecurityEvent({
        type: 'validation',
        severity: 'medium',
        message: error.message,
      })
    }
    originalConsoleError.apply(console, args)
  }

  // Monitor for suspicious DOM modifications
  if (typeof window !== 'undefined' && document.body) {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element

              // Check for suspicious script tags
              if (element.tagName === 'SCRIPT' && !element.hasAttribute('data-approved')) {
                security.logSecurityEvent({
                  type: 'xss_attempt',
                  severity: 'high',
                  message: 'Suspicious script tag detected',
                })

                // Remove the script tag
                element.remove()
              }

              // Check for suspicious iframe tags
              if (element.tagName === 'IFRAME' && !element.hasAttribute('data-approved')) {
                const src = element.getAttribute('src')
                if (src && !src.startsWith(window.location.origin)) {
                  security.logSecurityEvent({
                    type: 'xss_attempt',
                    severity: 'high',
                    message: 'Suspicious iframe detected',
                  })

                  // Remove the iframe
                  element.remove()
                }
              }
            }
          })
        }
      })
    })

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      observer.disconnect()
    })
  }

  // Provide security instance globally
  return {
    provide: {
      security,
    },
  }
})
